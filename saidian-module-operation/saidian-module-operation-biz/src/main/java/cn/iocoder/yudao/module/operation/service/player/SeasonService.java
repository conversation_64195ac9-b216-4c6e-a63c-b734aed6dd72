package cn.iocoder.yudao.module.operation.service.player;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.operation.controller.admin.season.vo.SeasonPageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.season.vo.SeasonSaveReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.season.vo.SeasonGamePageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.GameRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerGameRelatedRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.SeasonDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 赛季管理服务接口
 *
 * <AUTHOR> Assistant
 */
public interface SeasonService {

    /**
     * 创建赛季
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSeason(SeasonSaveReqVO createReqVO);

    /**
     * 更新赛季
     *
     * @param updateReqVO 更新信息
     */
    void updateSeason(SeasonSaveReqVO updateReqVO);

    /**
     * 删除赛季
     *
     * @param id 编号
     */
    void deleteSeason(Long id);

    /**
     * 获得赛季
     *
     * @param id 编号
     * @return 赛季
     */
    SeasonDO getSeason(Long id);

    /**
     * 获得赛季分页
     *
     * @param pageReqVO 分页查询
     * @return 赛季分页
     */
    PageResult<SeasonDO> getSeasonPage(SeasonPageReqVO pageReqVO);

    /**
     * 获取当前赛季
     */
    SeasonDO getCurrentSeason();

    /**
     * 根据日期获取赛季
     *
     * @param date 日期
     * @return 赛季信息
     */
    SeasonDO getSeasonByDate(LocalDate date);

    /**
     * 创建新赛季
     *
     * @param seasonName 赛季名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param seasonType 赛季类型
     * @return 创建的赛季
     */
    SeasonDO createSeason(String seasonName, LocalDate startDate, LocalDate endDate, Integer seasonType);

    /**
     * 初始化默认赛季
     */
    SeasonDO initializeDefaultSeason();

    /**
     * 为比赛分配赛季ID
     *
     * @param gameId 比赛ID
     * @param gameDate 比赛日期
     * @return 分配的赛季ID
     */
    Long assignSeasonToGame(Long gameId, LocalDate gameDate);

    /**
     * 获取所有赛季
     */
    List<SeasonDO> getAllSeasons();

    /**
     * 更新赛季统计信息
     *
     * @param seasonId 赛季ID
     */
    void updateSeasonStats(Long seasonId);

    /**
     * 自动赛季切换
     * 检查当前日期，如果需要切换赛季则自动切换
     *
     * @return 是否成功切换
     */
    boolean autoSwitchSeason();

    /**
     * 手动切换到指定赛季
     *
     * @param seasonId 目标赛季ID
     * @return 是否成功切换
     */
    boolean switchToSeason(Long seasonId);

    /**
     * 获取赛季比赛分页
     *
     * @param pageReqVO 分页查询
     * @return 比赛分页
     */
    PageResult<GameRespVO> getSeasonGamePage(SeasonGamePageReqVO pageReqVO);

    /**
     * 获取比赛参赛球员列表
     *
     * @param gameId 比赛ID
     * @return 参赛球员列表
     */
    List<PlayerGameRelatedRespVO> getGamePlayers(Long gameId);

    /**
     * 初始化比赛赛季关联
     * 根据比赛开始时间自动关联到对应赛季
     *
     * @return 更新的比赛数量
     */
    int initGameSeasons();
} 