package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerDataInitializerV2;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;

/**
 * 球员生涯统计服务
 * 
 * 负责球员生涯数据的计算、更新和维护
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Service
@Slf4j
public class PlayerCareerStatsService {

    @Resource
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    
    @Resource
    private PlayerStatisticsMapper playerStatisticsMapper;
    
    @Resource
    private PlayerGameRelatedMapper playerGameRelatedMapper;
    
    @Resource
    private PlayerCareerDataInitializerV2 careerDataInitializer;
    
    @Resource
    private PlayerCareerStatsCalculator playerCareerStatsCalculator;

    /**
     * 获取球员生涯统计数据（优先从聚合表读取）
     * 
     * @param playerId 球员ID
     * @param gameType 比赛类型
     * @return 生涯统计数据
     */
    public PlayerCareerStatsDO calculateCareerStats(Long playerId, Integer gameType) {
        if (playerId == null) {
            throw new IllegalArgumentException("球员ID不能为空");
        }
        
        if (gameType == null) {
            gameType = 0; // 默认为全部比赛类型
        }

        log.info("🏀 获取球员 {} 的生涯统计数据，比赛类型: {}", playerId, gameType);

        // 1. 优先从聚合表查询已计算的生涯数据
        PlayerCareerStatsDO existingStats = getCareerStatsFromDatabase(playerId, gameType);
        if (existingStats != null) {
            log.debug("✅ 从聚合表获取球员 {} 生涯数据: 总场次={}, 场均得分={}", 
                    playerId, existingStats.getGamesPlayed(), existingStats.getAvgPoints());
            return existingStats;
        }

        // 2. 如果聚合表没有数据，则实时计算并保存
        log.info("📊 聚合表无数据，开始实时计算球员 {} 的生涯统计", playerId);
        return calculateAndSaveCareerStats(playerId, gameType);
    }

    /**
     * 从聚合表获取生涯统计数据
     */
    private PlayerCareerStatsDO getCareerStatsFromDatabase(Long playerId, Integer gameType) {
        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getPlayerId, playerId)
               .eq(PlayerCareerStatsDO::getGameType, gameType)
               .eq(PlayerCareerStatsDO::getDeleted, false);
        
        return playerCareerStatsMapper.selectOne(wrapper);
    }

    /**
     * 实时计算并保存生涯统计数据
     */
    private PlayerCareerStatsDO calculateAndSaveCareerStats(Long playerId, Integer gameType) {
        // 1. 获取球员所有比赛统计数据（第0节汇总数据）
        List<PlayerStatisticsDO> allGameStats = getPlayerAllGameStats(playerId, gameType);
        
        if (allGameStats.isEmpty()) {
            log.warn("⚠️ 球员 {} 没有比赛统计数据", playerId);
            return createEmptyCareerStats(playerId, gameType);
        }

        // 2. 调用计算器进行数据聚合
        PlayerCareerStatsDO careerStats = playerCareerStatsCalculator.calculateFromGameStats(playerId, gameType, allGameStats);
        
        // 3. 计算连胜数据
        List<PlayerGameRelatedDO> gameResults = getPlayerGameResults(playerId, gameType);
        playerCareerStatsCalculator.calculateStreakData(careerStats, gameResults);

        // 4. 保存到聚合表
        try {
            saveCareerStatsToDatabase(careerStats);
            log.info("💾 球员 {} 生涯统计数据已保存到聚合表", playerId);
        } catch (Exception e) {
            log.error("❌ 保存球员 {} 生涯数据到聚合表失败", playerId, e);
        }

        log.info("✅ 球员 {} 生涯统计计算完成: 总场次={}, 场均得分={}", 
                playerId, careerStats.getGamesPlayed(), careerStats.getAvgPoints());
        
        return careerStats;
    }

    /**
     * 保存生涯统计数据到聚合表
     */
    private void saveCareerStatsToDatabase(PlayerCareerStatsDO careerStats) {
        PlayerCareerStatsDO existingStats = getCareerStatsFromDatabase(
            careerStats.getPlayerId(), careerStats.getGameType());
        
        if (existingStats != null) {
            // 更新现有记录
            careerStats.setId(existingStats.getId());
            playerCareerStatsMapper.updateById(careerStats);
        } else {
            // 插入新记录
            playerCareerStatsMapper.insert(careerStats);
        }
    }

    /**
     * 获取所有有效的生涯统计数据（用于计算联盟最佳）
     * 
     * @param gameType 比赛类型
     * @return 有效的生涯统计数据列表
     */
    public List<PlayerCareerStatsDO> getAllValidCareerStats(Integer gameType) {
        if (gameType == null) {
            gameType = 0; // 默认为全部比赛类型
        }

        try {
            LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PlayerCareerStatsDO::getGameType, gameType)
                   .eq(PlayerCareerStatsDO::getDeleted, false)
                   .ge(PlayerCareerStatsDO::getGamesPlayed, 5) // 至少5场比赛
                   .orderByDesc(PlayerCareerStatsDO::getAvgPoints); // 按场均得分倒序

            List<PlayerCareerStatsDO> allStats = playerCareerStatsMapper.selectList(wrapper);
            
            log.debug("🏆 从聚合表获取有效生涯统计数据: 比赛类型={}, 球员数={}", gameType, allStats.size());
            
            return allStats;
        } catch (Exception e) {
            log.error("获取所有有效生涯统计数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新球员生涯数据
     * 
     * @param playerId 球员ID
     * @param gameType 比赛类型
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePlayerCareerStats(Long playerId, Integer gameType) {
        if (playerId == null) {
            throw new IllegalArgumentException("球员ID不能为空");
        }
        
        if (gameType == null) {
            gameType = 0; // 默认为全部比赛类型
        }

        // 1. 计算最新的生涯统计数据
        PlayerCareerStatsDO newCareerStats = calculateCareerStats(playerId, gameType);
        
        // 2. 查找现有记录
        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getPlayerId, playerId)
               .eq(PlayerCareerStatsDO::getGameType, gameType)
               .eq(PlayerCareerStatsDO::getDeleted, false);
        
        PlayerCareerStatsDO existingStats = playerCareerStatsMapper.selectOne(wrapper);
        
        // 3. 保存或更新数据
        if (existingStats != null) {
            // 更新现有记录
            newCareerStats.setId(existingStats.getId());
            playerCareerStatsMapper.updateById(newCareerStats);
            log.info("🔄 更新球员 {} 的生涯统计数据", playerId);
        } else {
            // 插入新记录
            playerCareerStatsMapper.insert(newCareerStats);
            log.info("➕ 新增球员 {} 的生涯统计数据", playerId);
        }
    }

    /**
     * 批量初始化球员生涯数据
     * 
     * @param playerIds 球员ID列表
     * @return 初始化结果
     */
    @Transactional(rollbackFor = Exception.class)
    public InitializationResult batchInitializeCareerStats(List<Long> playerIds) {
        if (playerIds == null || playerIds.isEmpty()) {
            return InitializationResult.success("无需处理的球员", 0, 0);
        }

        log.info("🚀 开始批量初始化 {} 名球员的生涯数据", playerIds.size());

        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        // 分批处理，避免一次处理过多数据
        int batchSize = 100;
        for (int i = 0; i < playerIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, playerIds.size());
            List<Long> batch = playerIds.subList(i, endIndex);
            
            log.info("📊 处理批次 {}-{}/{}", i + 1, endIndex, playerIds.size());
            
            for (Long playerId : batch) {
                try {
                    // 初始化全部比赛类型的生涯数据
                    updatePlayerCareerStats(playerId, 0);
                    successCount++;
                } catch (Exception e) {
                    log.error("❌ 初始化球员 {} 生涯数据失败", playerId, e);
                    errors.add("球员 " + playerId + ": " + e.getMessage());
                    failCount++;
                }
            }
        }

        String message = String.format("批量初始化完成: 成功=%d, 失败=%d", successCount, failCount);
        if (!errors.isEmpty()) {
            message += ", 错误: " + String.join("; ", errors.subList(0, Math.min(5, errors.size())));
        }

        log.info("✅ {}", message);
        return InitializationResult.success(message, successCount, failCount);
    }

    /**
     * 获取球员所有比赛统计数据
     */
    private List<PlayerStatisticsDO> getPlayerAllGameStats(Long playerId, Integer gameType) {
        LambdaQueryWrapper<PlayerStatisticsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerStatisticsDO::getPlayerId, playerId)
               .eq(PlayerStatisticsDO::getSection, 0) // 只查第0节汇总数据
               .eq(PlayerStatisticsDO::getDeleted, false);
        
        // 如果指定了比赛类型，需要通过比赛表关联查询
        // 这里简化处理，暂时不按比赛类型过滤
        // TODO: 后续可以通过JOIN查询或者分步查询来实现比赛类型过滤
        
        return playerStatisticsMapper.selectList(wrapper);
    }

    /**
     * 获取球员比赛结果数据（用于计算连胜）
     */
    private List<PlayerGameRelatedDO> getPlayerGameResults(Long playerId, Integer gameType) {
        LambdaQueryWrapper<PlayerGameRelatedDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerGameRelatedDO::getPlayerId, playerId)
               .eq(PlayerGameRelatedDO::getDeleted, false)
               .orderByAsc(PlayerGameRelatedDO::getCreateTime); // 按时间顺序
        
        return playerGameRelatedMapper.selectList(wrapper);
    }

    /**
     * 创建空的生涯统计数据
     */
    private PlayerCareerStatsDO createEmptyCareerStats(Long playerId, Integer gameType) {
        PlayerCareerStatsDO emptyStats = new PlayerCareerStatsDO();
        emptyStats.setPlayerId(playerId);
        emptyStats.setGameType(gameType);
        // 其他字段使用默认值0
        return emptyStats;
    }

    /**
     * 强制重新计算球员生涯数据
     * 用于数据修复场景
     * 
     * @param playerId 球员ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void forceRecalculateCareerStats(Long playerId) {
        if (playerId == null) {
            throw new IllegalArgumentException("球员ID不能为空");
        }

        log.info("🔧 强制重新计算球员 {} 的生涯数据", playerId);

        // 重新计算所有比赛类型的生涯数据
        updatePlayerCareerStats(playerId, 0); // 全部比赛
        updatePlayerCareerStats(playerId, 1); // 排位赛
        updatePlayerCareerStats(playerId, 2); // 友谊赛
        updatePlayerCareerStats(playerId, 3); // 联赛

        log.info("✅ 球员 {} 生涯数据强制重算完成", playerId);
    }

    /**
     * 轻量级更新：只更新胜负相关统计数据
     * 用于比赛编辑场景，避免重新计算所有统计数据
     * 
     * @param playerId 球员ID
     * @param gameId 比赛ID
     */
    public void updateWinLossStats(Long playerId, Long gameId) {
        if (playerId == null || gameId == null) {
            throw new IllegalArgumentException("球员ID和比赛ID不能为空");
        }

        log.info("⚽ 开始轻量级更新球员 {} 的胜负数据，比赛ID: {}", playerId, gameId);

        try {
            // 获取该球员在这场比赛中的结果
            List<PlayerGameRelatedDO> gameResults = getPlayerGameResultsForGame(playerId, gameId);
            
            if (gameResults.isEmpty()) {
                log.warn("⚠️ 未找到球员 {} 在比赛 {} 中的结果数据", playerId, gameId);
                return;
            }

            // 重新计算所有比赛类型的胜负数据
            for (int gameType = 0; gameType <= 3; gameType++) {
                updateWinLossStatsForGameType(playerId, gameType);
            }

            log.info("✅ 球员 {} 胜负数据轻量级更新完成", playerId);

        } catch (Exception e) {
            log.error("❌ 轻量级更新球员 {} 胜负数据失败", playerId, e);
            throw e;
        }
    }

    /**
     * 更新时间相关的统计数据
     * 主要更新首场比赛日期、最近比赛日期等时间字段
     * 
     * @param playerId 球员ID
     */
    public void updateGameTimeRelatedStats(Long playerId) {
        if (playerId == null) {
            throw new IllegalArgumentException("球员ID不能为空");
        }

        log.info("⏰ 开始更新球员 {} 的时间相关数据", playerId);

        try {
            // 获取球员所有比赛数据，重新计算时间字段
            List<PlayerStatisticsDO> allGameStats = getPlayerAllGameStats(playerId, 0);
            
            if (allGameStats.isEmpty()) {
                log.warn("⚠️ 球员 {} 没有比赛数据", playerId);
                return;
            }

            // 为每个比赛类型更新时间相关字段
            for (int gameType = 0; gameType <= 3; gameType++) {
                updateTimeRelatedFieldsForGameType(playerId, gameType, allGameStats);
            }

            log.info("✅ 球员 {} 时间相关数据更新完成", playerId);

        } catch (Exception e) {
            log.error("❌ 更新球员 {} 时间相关数据失败", playerId, e);
            throw e;
        }
    }

    /**
     * 为指定比赛类型更新胜负统计
     */
    private void updateWinLossStatsForGameType(Long playerId, Integer gameType) {
        try {
            // 获取现有的生涯统计数据
            PlayerCareerStatsDO existingStats = getCareerStatsFromDatabase(playerId, gameType);
            if (existingStats == null) {
                // 如果不存在，触发完整计算
                forceRecalculateCareerStats(playerId);
                return;
            }

            // 重新计算胜负数据
            List<PlayerGameRelatedDO> gameResults = getPlayerGameResults(playerId, gameType);
            playerCareerStatsCalculator.calculateStreakData(existingStats, gameResults);

            // 更新到数据库
            playerCareerStatsMapper.updateById(existingStats);

            log.debug("✅ 球员 {} 比赛类型 {} 的胜负数据已更新", playerId, gameType);

        } catch (Exception e) {
            log.error("❌ 更新球员 {} 比赛类型 {} 胜负数据失败", playerId, gameType, e);
        }
    }

    /**
     * 为指定比赛类型更新时间相关字段
     */
    private void updateTimeRelatedFieldsForGameType(Long playerId, Integer gameType, List<PlayerStatisticsDO> allGameStats) {
        try {
            // 获取现有的生涯统计数据
            PlayerCareerStatsDO existingStats = getCareerStatsFromDatabase(playerId, gameType);
            if (existingStats == null) {
                return;
            }

            // 重新计算时间相关字段
            playerCareerStatsCalculator.calculateTimeRelatedFields(existingStats, allGameStats);

            // 更新到数据库
            playerCareerStatsMapper.updateById(existingStats);

            log.debug("✅ 球员 {} 比赛类型 {} 的时间相关数据已更新", playerId, gameType);

        } catch (Exception e) {
            log.error("❌ 更新球员 {} 比赛类型 {} 时间相关数据失败", playerId, gameType, e);
        }
    }

    /**
     * 获取球员在特定比赛中的结果数据
     */
    private List<PlayerGameRelatedDO> getPlayerGameResultsForGame(Long playerId, Long gameId) {
        LambdaQueryWrapper<PlayerGameRelatedDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerGameRelatedDO::getPlayerId, playerId)
               .eq(PlayerGameRelatedDO::getGameId, gameId)
               .eq(PlayerGameRelatedDO::getDeleted, false);
        
        return playerGameRelatedMapper.selectList(wrapper);
    }
} 