package cn.iocoder.yudao.module.operation.service.career.impl;

import cn.iocoder.yudao.module.operation.controller.admin.player.vo.PlayerCareerVO;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.AbilityTrendVO;
import cn.iocoder.yudao.module.operation.convert.player.PlayerCareerConvert;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerBestStatsDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerBestStatsMapper;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerService;
import cn.iocoder.yudao.module.operation.service.stats.calculator.impl.CareerStatsCalculator;
import cn.iocoder.yudao.module.operation.enums.GameTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 球员生涯服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlayerCareerServiceImpl implements PlayerCareerService {

    @Resource
    private PlayerMapper playerMapper;

    @Resource
    private PlayerCareerStatsMapper playerCareerStatsMapper;

    @Resource
    private PlayerCareerBestStatsMapper playerCareerBestStatsMapper;

    @Resource
    private CareerStatsCalculator careerStatsCalculator;

    @Resource
    private PlayerCareerConvert playerCareerConvert;

    /**
     * 用于生成随机趋势数据的Random实例
     */
    private final Random random = new Random();

    /**
     * 全部比赛类型常量（用于统计所有类型的数据）
     */
    private static final int ALL_GAME_TYPE = 0;

    @Override
    public PlayerCareerVO getPlayerCareer(Long playerId) {
        // 获取球员基础信息
        PlayerDO player = playerMapper.selectById(playerId);
        if (player == null) {
            throw new IllegalArgumentException("球员不存在: " + playerId);
        }

        // 获取生涯统计数据
        List<PlayerCareerStatsDO> careerStatsList = playerCareerStatsMapper.selectList(
                new LambdaQueryWrapperX<PlayerCareerStatsDO>()
                        .eq(PlayerCareerStatsDO::getPlayerId, playerId));

        // 获取生涯最佳数据
        List<PlayerCareerBestStatsDO> careerBestStatsList = playerCareerBestStatsMapper.selectList(
                new LambdaQueryWrapperX<PlayerCareerBestStatsDO>()
                        .eq(PlayerCareerBestStatsDO::getPlayerId, playerId));

        // 转换为VO
        return playerCareerConvert.convertToCareerVO(player, careerStatsList, careerBestStatsList);
    }

    @Override
    public boolean refreshPlayerCareerStats(Long playerId) {
        try {
            // 刷新全部比赛类型的生涯统计
            boolean success = true;

            // 刷新全部类型统计（gameType = 0）
            boolean allTypeResult = careerStatsCalculator.calculateStats(playerId, ALL_GAME_TYPE);
            success = success && allTypeResult;

            // 刷新各个具体比赛类型的统计
            for (GameTypeEnum gameType : GameTypeEnum.values()) {
                boolean result = careerStatsCalculator.calculateStats(playerId, gameType.getType());
                success = success && result;
            }

            log.info("球员生涯统计刷新完成 - 球员ID: {}, 结果: {}", playerId, success);
            return success;
        } catch (Exception e) {
            log.error("球员生涯统计刷新失败 - 球员ID: {}", playerId, e);
            return false;
        }
    }

    @Override
    public boolean refreshPlayerCareerStatsByGameType(Long playerId, Integer gameType) {
        try {
            boolean success = careerStatsCalculator.calculateStats(playerId, gameType);
            
            String gameTypeName = getGameTypeName(gameType);
            log.info("球员生涯统计刷新完成 - 球员ID: {}, 比赛类型: {}({}), 结果: {}", 
                    playerId, gameType, gameTypeName, success);
            return success;
        } catch (Exception e) {
            log.error("球员生涯统计刷新失败 - 球员ID: {}, 比赛类型: {}", playerId, gameType, e);
            return false;
        }
    }

    @Override
    public List<AbilityTrendVO> getPlayerAbilityTrend(Long playerId) {
        log.info("获取球员能力值趋势 - 球员ID: {}", playerId);

        try {
            // 获取球员基础信息
            PlayerDO player = playerMapper.selectById(playerId);
            if (player == null) {
                throw new IllegalArgumentException("球员不存在: " + playerId);
            }

            // 生成最近30天的能力值趋势数据
            List<AbilityTrendVO> trendList = new ArrayList<>();

            // 获取当前能力值作为基准
            Integer currentRating = player.getRatings() != null ? player.getRatings() : 1500;

            // 生成最近30天的趋势数据
            for (int i = 29; i >= 0; i--) {
                AbilityTrendVO trend = new AbilityTrendVO();

                // 设置记录时间（最近30天）
                LocalDateTime trendDateTime = LocalDateTime.now().minusDays(i);
                trend.setRecordTime(trendDateTime);

                // 模拟能力值波动（基于正态分布的小幅波动）
                double variance = generateRandomVariance();
                BigDecimal overallRating = calculateRatingWithVariance(currentRating, variance);
                trend.setOverallRating(overallRating);

                // 生成7维度评分（基于总评分的随机分布）
                generateSevenDimensionScores(trend, overallRating);

                // 设置变化原因
                trend.setChangeReason(generateChangeReason());

                // 模拟关联比赛ID（可能为空，表示不是比赛引起的变化）
                if (random.nextDouble() < 0.7) { // 70%的概率有关联比赛
                    trend.setGameId(random.nextLong(1000) + 1); // 随机比赛ID
                }

                trendList.add(trend);
            }

            log.debug("生成能力值趋势数据 - 球员ID: {}, 数据点数: {}", playerId, trendList.size());
            return trendList;

        } catch (Exception e) {
            log.error("获取球员能力值趋势失败 - 球员ID: {}", playerId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean refreshAllPlayersCareerStats() {
        try {
            // 获取所有球员
            List<PlayerDO> allPlayers = playerMapper.selectList();

            int successCount = 0;
            int totalCount = allPlayers.size();

            for (PlayerDO player : allPlayers) {
                boolean success = refreshPlayerCareerStats(player.getId());
                if (success) {
                    successCount++;
                }
            }

            log.info("批量刷新球员生涯数据完成 - 总数: {}, 成功: {}", totalCount, successCount);
            return successCount == totalCount;
        } catch (Exception e) {
            log.error("批量刷新球员生涯数据失败", e);
            return false;
        }
    }

    // ===================== 私有辅助方法 =====================

    /**
     * 获取比赛类型名称
     */
    private String getGameTypeName(Integer gameType) {
        if (gameType == null) {
            return "未知";
        }
        
        if (gameType == ALL_GAME_TYPE) {
            return "全部";
        }

        try {
            return GameTypeEnum.getDescription(gameType);
        } catch (IllegalArgumentException e) {
            return "未知类型(" + gameType + ")";
        }
    }

    /**
     * 生成随机方差
     */
    private double generateRandomVariance() {
        // 使用正态分布生成 -15 到 +15 的随机波动
        return (random.nextGaussian() * 5.0); // 标准差为5，约99.7%的值在[-15, +15]范围内
    }

    /**
     * 计算带方差的能力值
     */
    private BigDecimal calculateRatingWithVariance(Integer baseRating, double variance) {
        double newRating = baseRating + variance;
        // 限制在合理范围内 [1000, 2000]
        newRating = Math.max(1000, Math.min(2000, newRating));
        return BigDecimal.valueOf(newRating).setScale(1, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 生成7维度评分
     */
    private void generateSevenDimensionScores(AbilityTrendVO trend, BigDecimal overallRating) {
        // 基于总评分生成各维度评分，保持一定的随机性但又不会偏离太多
        double baseScore = overallRating.doubleValue() / 20.0; // 转换为100分制
        
        trend.setEfficiencyScore(generateDimensionScore(baseScore));
        trend.setScoringScore(generateDimensionScore(baseScore));
        trend.setReboundingScore(generateDimensionScore(baseScore));
        trend.setAssistingScore(generateDimensionScore(baseScore));
        trend.setDefenseScore(generateDimensionScore(baseScore));
        trend.setTurnoverScore(generateDimensionScore(baseScore));
        trend.setFoulScore(generateDimensionScore(baseScore));
    }

    /**
     * 生成单个维度评分
     */
    private BigDecimal generateDimensionScore(double baseScore) {
        // 在基础分数上下浮动20%
        double variance = (random.nextDouble() - 0.5) * 0.4 * baseScore; // ±20%
        double score = baseScore + variance;
        // 限制在0-100范围内
        score = Math.max(0, Math.min(100, score));
        return BigDecimal.valueOf(score).setScale(1, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 生成变化原因
     */
    private String generateChangeReason() {
        List<String> reasons = Arrays.asList(
                "比赛表现优异",
                "比赛表现一般",
                "训练提升",
                "自然波动",
                "连胜状态",
                "状态调整",
                "技术改进",
                "体能提升"
        );
        return reasons.get(random.nextInt(reasons.size()));
    }
} 