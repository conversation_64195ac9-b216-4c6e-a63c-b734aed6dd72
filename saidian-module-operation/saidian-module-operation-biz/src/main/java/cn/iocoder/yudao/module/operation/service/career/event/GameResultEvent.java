package cn.iocoder.yudao.module.operation.service.career.event;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 比赛结果事件
 * 
 * 当比赛结束时发布此事件，触发相关球员生涯数据的更新
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GameResultEvent extends ApplicationEvent {

    private Long gameId;
    private Long playerId;
    private String gameResult; // "win" 或 "lose"
    private LocalDateTime gameTime;

    public GameResultEvent(Object source, Long gameId, Long playerId, String gameResult, LocalDateTime gameTime) {
        super(source);
        this.gameId = gameId;
        this.playerId = playerId;
        this.gameResult = gameResult;
        this.gameTime = gameTime;
    }

    /**
     * 批量比赛结果事件
     * 用于一场比赛多个球员的情况
     */
    @Data
    public static class BatchGameResultEvent extends ApplicationEvent {
        
        private Long gameId;
        private List<Long> playerIds;
        private LocalDateTime gameTime;

        public BatchGameResultEvent(Object source, Long gameId, List<Long> playerIds, LocalDateTime gameTime) {
            super(source);
            this.gameId = gameId;
            this.playerIds = playerIds;
            this.gameTime = gameTime;
        }
    }
}