package cn.iocoder.yudao.module.operation.controller.app.player;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.*;
import cn.iocoder.yudao.module.operation.convert.player.PlayerConvert;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerSeasonStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerBestStatsDO;

import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.enums.PlayerPositionEnum;
import cn.iocoder.yudao.module.operation.service.game.PlayerStatisticsService;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.game.bo.PlayerCareerStatistics;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerStatsService;
import cn.iocoder.yudao.module.operation.service.career.PlayerSeasonStatsService;
import cn.iocoder.yudao.module.operation.service.career.PlayerBestStatsService;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerOverviewService;
import cn.iocoder.yudao.module.operation.service.career.RadarChartCacheService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "App - 球员")
@RestController
@RequestMapping("/league/player")
@Validated
@Slf4j
public class AppPlayerController {

    @Resource
    private PlayerService playerService;
    @Resource
    private PlayerStatisticsService playerStatisticsService;
    @Resource
    private GameService gameService;
    @Resource
    private PlayerCareerStatsService playerCareerStatsService;
    @Resource
    private PlayerSeasonStatsService playerSeasonStatsService;
    @Resource
    private PlayerBestStatsService playerBestStatsService;
    @Resource
    private PlayerCareerOverviewService playerCareerOverviewService;

    @Resource
    private RadarChartCacheService radarChartCacheService;

    @GetMapping("/get")
    @Operation(summary = "获得基本信息")
    public CommonResult<AppPlayerInfoRespVO> getPlayerInfo(@RequestParam(name = "userId", required = false) Long userId, @RequestParam(name = "playerId", required = false) Long playerId) {
        if (playerId != null) {
            PlayerDO player = playerService.getPlayer(playerId);
            return success(PlayerConvert.INSTANCE.convert(player));
        }

        if (userId == null) {
            userId = getLoginUserId();
        }

        PlayerDO player = playerService.getPlayerByUserId(userId);
        return success(PlayerConvert.INSTANCE.convert(player));
    }

    @GetMapping("/info/get")
    @Operation(summary = "获得基本信息")
    public CommonResult<List<AppPlayerIntroductionRespVO>> getPlayerDetailInfo(@RequestParam(name = "playerId") Long playerId) {
        if (playerId == null) {
            log.warn("[getPlayerDetailInfo] playerId为空");
            return success(new ArrayList<>());
        }

        PlayerDO player = playerService.getPlayer(playerId);
        List<AppPlayerIntroductionRespVO> introductions = new ArrayList<>();

        if (player != null) {
            try {
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("姓名").setValue(player.getName() != null ? player.getName() : "未设置"));
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("号码").setValue(player.getNumber() != null ? player.getNumber() + "号" : "未设置"));
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("身高").setValue(player.getHeight() != null ? player.getHeight() + "cm" : "未设置"));
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("体重").setValue(player.getWeight() != null ? player.getWeight() + "kg" : "未设置"));
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("位置").setValue(PlayerPositionEnum.getChineseName(player.getPosition())));
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("信用度").setValue(String.valueOf(player.getCreditScore() != null ? player.getCreditScore() : 0)));
            } catch (Exception e) {
                log.error("[getPlayerDetailInfo] 构建球员信息时发生异常: playerId={}", playerId, e);
                introductions.add(new AppPlayerIntroductionRespVO().setLabel("错误").setValue("数据加载异常，请稍后重试"));
            }
        } else {
            log.warn("[getPlayerDetailInfo] 未找到球员信息: playerId={}", playerId);
            introductions.add(new AppPlayerIntroductionRespVO().setLabel("提示").setValue("球员信息不存在"));
        }

        return success(introductions);
    }

    @GetMapping("/statistics/get")
    @Operation(summary = "获得生涯")
    public CommonResult<AppPlayerStatisticsRespVO> getStatistics(@RequestParam(name = "playerId") Long playerId) {
        AppPlayerStatisticsRespVO playerCareerRespVOs = playerStatisticsService.getPlayerCareerStatisticsByPlayerId(playerId);
        return success(playerCareerRespVOs);
    }

    @PostMapping("/create")
    @Operation(summary = "获得基本信息")
    public CommonResult<PlayerDO> createPlayerInfo(@RequestBody @Valid AppPlayerInfoCreateReqVO appPlayerInfoCreateReqVO) {
        Long userId = getLoginUserId();
        return success(playerService.createPlayer(userId, appPlayerInfoCreateReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更改球员信息")
    public CommonResult<PlayerDO> updatePlayerInfo(@RequestBody @Valid AppPlayerInfoUpdateReqVO appPlayerInfoUpdateReqVO) {
        Long userId = getLoginUserId();
        return success(playerService.updatePlayer(userId, appPlayerInfoUpdateReqVO));
    }

    @GetMapping("/rank/page")
    @Operation(summary = "获得球员排名分页")
    @PermitAll
    public CommonResult<PageResult<AppPlayerRankRespVO>> getPlayerRankPage(@Valid AppPlayerRankPageReqVO pageVO) {
        // 获取所有球员生涯数据统计
        Map<Long, PlayerCareerStatistics> playerCareerStatisticsMap = playerStatisticsService.getPlayerCareerStatisticsMap();
        PageResult<AppPlayerRankRespVO> pageResult = playerStatisticsService.getPlayerRankPage(pageVO, new ArrayList<>(playerCareerStatisticsMap.values()));
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty(pageResult.getTotal()));
        }
        return success(pageResult);
    }

    @GetMapping("/market/page")
    @Operation(summary = "获得市场球员列表")
    public CommonResult<PageResult<AppPlayerMarketInfoRespVO>> getTeamMarketPageList(AppPlayerMarketPageReqVO pageReqVO) {
        return success(playerService.getPlayerMarketPage(pageReqVO));
    }

    @GetMapping("/career/overview")
    @Operation(summary = "获得球员生涯概览数据（核心数据网格+雷达图）")
    public CommonResult<AppPlayerCareerOverviewRespVO> getPlayerCareerOverview(@Valid AppPlayerCareerQueryReqVO reqVO) {
        log.info("获取球员生涯概览数据，参数：{}", reqVO);

        try {
            // 使用专门的Service处理业务逻辑
            AppPlayerCareerOverviewRespVO response = playerCareerOverviewService.getPlayerCareerOverview(reqVO);
            return success(response);
        } catch (Exception e) {
            log.error("获取球员生涯概览数据失败", e);
            return success(new AppPlayerCareerOverviewRespVO());
        }
    }

    @GetMapping("/career/best-stats")
    @Operation(summary = "获得球员最佳数据记录")
    public CommonResult<AppPlayerBestStatsRespVO> getPlayerBestStats(@Valid AppPlayerCareerQueryReqVO reqVO) {
        log.info("获取球员最佳数据记录，参数：{}", reqVO);

        try {
            AppPlayerBestStatsRespVO response = buildRealBestStats(reqVO);
            return success(response);
        } catch (Exception e) {
            log.error("获取球员最佳数据记录失败", e);
            return success(new AppPlayerBestStatsRespVO());
        }
    }

    @GetMapping("/seasons")
    @Operation(summary = "获得可用赛季列表")
    @PermitAll
    public CommonResult<List<AppPlayerSeasonOptionRespVO>> getAvailableSeasons() {
        log.info("获取可用赛季列表");

        try {
            List<AppPlayerSeasonOptionRespVO> seasons = buildRealSeasonOptions();
            return success(seasons);
        } catch (Exception e) {
            log.error("获取可用赛季列表失败", e);
            return success(new ArrayList<>());
        }
    }

    @GetMapping("/career/team-history")
    @Operation(summary = "获得球员球队经历")
    public CommonResult<List<AppPlayerTeamHistoryRespVO>> getPlayerTeamHistory(@RequestParam(name = "playerId", required = false) Long playerId) {
        log.info("获取球员球队经历，playerId: {}", playerId);

        try {
            if (playerId == null) {
                Long userId = getLoginUserId();
                PlayerDO player = playerService.getPlayerByUserId(userId);
                playerId = player != null ? player.getId() : null;
            }

            if (playerId == null) {
                return success(new ArrayList<>());
            }

            List<AppPlayerTeamHistoryRespVO> teamHistory = buildPlayerTeamHistory(playerId);
            return success(teamHistory);
        } catch (Exception e) {
            log.error("获取球员球队经历失败", e);
            return success(new ArrayList<>());
        }
    }

    @GetMapping("/career/honors")
    @Operation(summary = "获得球员荣誉数据")
    public CommonResult<List<AppPlayerHonorRespVO>> getPlayerHonors(@RequestParam(name = "playerId", required = false) Long playerId) {
        log.info("获取球员荣誉数据，playerId: {}", playerId);

        try {
            if (playerId == null) {
                Long userId = getLoginUserId();
                PlayerDO player = playerService.getPlayerByUserId(userId);
                playerId = player != null ? player.getId() : null;
            }

            if (playerId == null) {
                return success(new ArrayList<>());
            }

            List<AppPlayerHonorRespVO> honors = buildPlayerHonors(playerId);
            return success(honors);
        } catch (Exception e) {
            log.error("获取球员荣誉数据失败", e);
            return success(new ArrayList<>());
        }
    }

    @GetMapping("/career/data-stats")
    @Operation(summary = "获得球员详细数据统计")
    public CommonResult<AppPlayerDataStatsRespVO> getPlayerDataStats(@Valid AppPlayerCareerQueryReqVO reqVO) {
        log.info("获取球员详细数据统计，参数：{}", reqVO);

        try {
            AppPlayerDataStatsRespVO response = buildPlayerDataStats(reqVO);
            return success(response);
        } catch (Exception e) {
            log.error("获取球员详细数据统计失败", e);
            return success(new AppPlayerDataStatsRespVO());
        }
    }

    @GetMapping("/career/match-list")
    @Operation(summary = "获得球员比赛列表")
    public CommonResult<PageResult<AppPlayerMatchRespVO>> getPlayerMatchList(@Valid AppPlayerMatchPageReqVO pageReqVO) {
        log.info("获取球员比赛列表，参数：{}", pageReqVO);

        try {
            PageResult<AppPlayerMatchRespVO> response = buildPlayerMatchList(pageReqVO);
            return success(response);
        } catch (Exception e) {
            log.error("获取球员比赛列表失败", e);
            return success(PageResult.empty());
        }
    }

    /**
     * 构建真实的球员生涯概览数据
     */
    private AppPlayerCareerOverviewRespVO buildRealCareerOverview(AppPlayerCareerQueryReqVO reqVO) {
        AppPlayerCareerOverviewRespVO response = new AppPlayerCareerOverviewRespVO();

        // 获取球员ID
        Long playerId = getPlayerIdFromRequest(reqVO);
        if (playerId == null) {
            return response;
        }

        // 设置当前赛季
        String currentSeason = getCurrentSeason();
        response.setCurrentSeason(currentSeason);

        // 构建可用赛季列表
        response.setAvailableSeasons(buildAvailableSeasons(currentSeason));

        // 构建核心统计数据
        response.setCoreStats(buildRealCoreStats(playerId, reqVO.getGameType()));

        // 构建雷达图数据
        response.setRadarChart(buildRealRadarChart(playerId, reqVO.getGameType()));

        return response;
    }

    /**
     * 构建真实的核心统计数据
     */
    private AppPlayerCareerOverviewRespVO.CoreStatsData buildRealCoreStats(Long playerId, Integer gameType) {
        AppPlayerCareerOverviewRespVO.CoreStatsData coreStats = new AppPlayerCareerOverviewRespVO.CoreStatsData();

        // 获取生涯统计数据
        PlayerCareerStatsDO careerStats = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        if (careerStats != null) {
            // 基础统计数据
            List<AppPlayerCareerOverviewRespVO.StatItem> basicStats = Arrays.asList(
                    createStatItem("场均得分", formatDecimal(careerStats.getAvgPoints()), "分", "number"),
                    createStatItem("场均篮板", formatDecimal(careerStats.getAvgRebounds()), "个", "number"),
                    createStatItem("场均助攻", formatDecimal(careerStats.getAvgAssists()), "次", "number"),
                    createStatItem("场均抢断", formatDecimal(careerStats.getAvgSteals()), "次", "number"),
                    createStatItem("场均盖帽", formatDecimal(careerStats.getAvgBlocks()), "次", "number"),
                    createStatItem("场均失误", formatDecimal(careerStats.getAvgTurnovers()), "次", "number")
            );
            coreStats.setBasicStats(basicStats);

            // 命中率统计数据
            List<AppPlayerCareerOverviewRespVO.StatItem> shootingStats = Arrays.asList(
                    createStatItem("投篮命中率", formatPercentage(careerStats.getFieldGoalPercentage()), "%", "percentage"),
                    createStatItem("三分命中率", formatPercentage(careerStats.getThreePointPercentage()), "%", "percentage"),
                    createStatItem("二分命中率", formatPercentage(careerStats.getTwoPointPercentage()), "%", "percentage"),
                    createStatItem("罚球命中率", formatPercentage(careerStats.getFreeThrowPercentage()), "%", "percentage")
            );
            coreStats.setShootingStats(shootingStats);

            // 高阶统计数据
            List<AppPlayerCareerOverviewRespVO.StatItem> advancedStats = Arrays.asList(
                    createStatItem("效率值", formatDecimal(careerStats.getAvgEfficiency()), "", "number"),
                    createStatItem("真实命中率", formatPercentage(careerStats.getTrueShootingPercentage()), "%", "percentage"),
                    createStatItem("使用率", formatPercentage(careerStats.getUsageRate()), "%", "percentage"),
                    createStatItem("净胜分", formatDecimal(careerStats.getPlusMinus()), "", "number")
            );
            coreStats.setAdvancedStats(advancedStats);

            // 连胜统计数据
            List<AppPlayerCareerOverviewRespVO.StatItem> streakStats = Arrays.asList(
                    createStatItem("当前连胜", String.valueOf(careerStats.getCurrentStreak()), "场", "number"),
                    createStatItem("最大连胜", String.valueOf(careerStats.getMaxWinStreak()), "场", "number"),
                    createStatItem("生涯最大连胜", String.valueOf(careerStats.getMaxWinStreak()), "场", "number"),
                    createStatItem("胜率", formatPercentage(careerStats.getWinRate()), "%", "percentage")
            );
            coreStats.setStreakStats(streakStats);
        } else {
            // 如果没有数据，返回默认值
            coreStats.setBasicStats(createDefaultBasicStats());
            coreStats.setShootingStats(createDefaultShootingStats());
            coreStats.setAdvancedStats(createDefaultAdvancedStats());
            coreStats.setStreakStats(createDefaultStreakStats());
        }

        return coreStats;
    }

    /**
     * 构建真实的雷达图数据 - 显示球员与各维度最佳球员的对比（带缓存优化）
     */
    private AppPlayerCareerOverviewRespVO.RadarChartData buildRealRadarChart(Long playerId, Integer gameType) {
        // 1. 尝试从缓存获取雷达图数据
        AppPlayerCareerOverviewRespVO.RadarChartData cachedRadarChart = radarChartCacheService.getRadarChartData(playerId, gameType);
        if (cachedRadarChart != null) {
            return cachedRadarChart;
        }

        // 2. 缓存未命中，构建新的雷达图数据
        AppPlayerCareerOverviewRespVO.RadarChartData radarChart = new AppPlayerCareerOverviewRespVO.RadarChartData();

        // 获取球员生涯统计数据
        PlayerCareerStatsDO careerStats = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        if (careerStats != null) {
            // 获取联盟最佳数据（带缓存）
            LeagueBestStats leagueBest = getLeagueBestStatsWithCache(gameType);

            // 构建雷达图数据点 - 显示得分、篮板、助攻、盖帽、抢断五个维度
            List<AppPlayerCareerOverviewRespVO.RadarDataPoint> dataPoints = Arrays.asList(
                    createRadarDataPointWithDescription("得分",
                            calculateBestComparisonScore(careerStats.getAvgPoints(), leagueBest.getBestPoints()),
                            BigDecimal.valueOf(100),
                            "场均得分与联盟最佳对比"),
                    createRadarDataPointWithDescription("篮板",
                            calculateBestComparisonScore(careerStats.getAvgRebounds(), leagueBest.getBestRebounds()),
                            BigDecimal.valueOf(100),
                            "场均篮板与联盟最佳对比"),
                    createRadarDataPointWithDescription("助攻",
                            calculateBestComparisonScore(careerStats.getAvgAssists(), leagueBest.getBestAssists()),
                            BigDecimal.valueOf(100),
                            "场均助攻与联盟最佳对比"),
                    createRadarDataPointWithDescription("盖帽",
                            calculateBestComparisonScore(careerStats.getAvgBlocks(), leagueBest.getBestBlocks()),
                            BigDecimal.valueOf(100),
                            "场均盖帽与联盟最佳对比"),
                    createRadarDataPointWithDescription("抢断",
                            calculateBestComparisonScore(careerStats.getAvgSteals(), leagueBest.getBestSteals()),
                            BigDecimal.valueOf(100),
                            "场均抢断与联盟最佳对比")
            );
            radarChart.setDataPoints(dataPoints);

            // 计算综合评分（相对于各维度最佳球员）
            double avgRating = dataPoints.stream()
                    .mapToDouble(point -> point.getScore().doubleValue())
                    .average()
                    .orElse(50.0); // 默认50表示中等水平
            radarChart.setOverallRating(BigDecimal.valueOf(avgRating).setScale(1, RoundingMode.HALF_UP));
            radarChart.setSampleGames(careerStats.getGamesPlayed());
            radarChart.setRatingDescription("基于生涯" + careerStats.getGamesPlayed() + "场比赛数据，与各维度最佳球员对比");
        } else {
            // 如果没有数据，返回默认雷达图
            radarChart.setDataPoints(createDefaultRadarDataForBestComparison());
            radarChart.setOverallRating(BigDecimal.valueOf(50.0)); // 50表示中等水平
            radarChart.setSampleGames(0);
            radarChart.setRatingDescription("暂无比赛数据");
        }

        // 3. 缓存雷达图数据
        radarChartCacheService.cacheRadarChartData(playerId, gameType, radarChart);

        return radarChart;
    }

    /**
     * 获取联盟最佳数据（带缓存）
     */
    private LeagueBestStats getLeagueBestStatsWithCache(Integer gameType) {
        // 1. 尝试从缓存获取
        LeagueBestStats cachedStats = radarChartCacheService.getLeagueBestStats(gameType);
        if (cachedStats != null) {
            return cachedStats;
        }

        // 2. 重新计算
        LeagueBestStats leagueBest = getLeagueBestStats(gameType);

        // 3. 缓存结果
        radarChartCacheService.cacheLeagueBestStats(gameType, leagueBest);

        return leagueBest;
    }

    /**
     * 构建真实的最佳数据记录
     */
    private AppPlayerBestStatsRespVO buildRealBestStats(AppPlayerCareerQueryReqVO reqVO) {
        AppPlayerBestStatsRespVO response = new AppPlayerBestStatsRespVO();

        // 获取球员ID
        Long playerId = getPlayerIdFromRequest(reqVO);
        if (playerId == null) {
            return response;
        }

        // 获取最佳数据记录
        String scope = reqVO.getSeason() != null ? "season" : "career";

        try {
            if ("season".equals(scope)) {
                // 获取赛季最佳数据
                Object seasonBestStats = playerBestStatsService.getBestStatsRecord(playerId, "season");
                if (seasonBestStats instanceof PlayerSeasonStatsDO) {
                    response.setSeasonBest(buildSeasonBestStatsList((PlayerSeasonStatsDO) seasonBestStats));
                }
            } else {
                // 生涯最佳数据暂不支持，返回空数据
                log.warn("生涯最佳数据暂不支持，返回空数据");
                response.setCareerBest(new ArrayList<>());
            }
        } catch (Exception e) {
            log.warn("获取最佳数据记录失败，使用空数据: {}", e.getMessage());
            response.setSeasonBest(new ArrayList<>());
            response.setCareerBest(new ArrayList<>());
        }

        return response;
    }

    /**
     * 构建赛季最佳数据列表
     */
    private List<AppPlayerBestStatsRespVO.BestStatItem> buildSeasonBestStatsList(PlayerSeasonStatsDO seasonStats) {
        List<AppPlayerBestStatsRespVO.BestStatItem> bestStats = new ArrayList<>();

        if (seasonStats.getBestPoints() != null && seasonStats.getBestPoints() > 0) {
            bestStats.add(createBestStatItem("单场最高得分",
                    String.valueOf(seasonStats.getBestPoints()), "分",
                    null, seasonStats.getBestPointsGameId(),
                    "未知对手", "未知结果"));
        }

        if (seasonStats.getBestRebounds() != null && seasonStats.getBestRebounds() > 0) {
            bestStats.add(createBestStatItem("单场最高篮板",
                    String.valueOf(seasonStats.getBestRebounds()), "个",
                    null, seasonStats.getBestReboundsGameId(),
                    "未知对手", "未知结果"));
        }

        if (seasonStats.getBestAssists() != null && seasonStats.getBestAssists() > 0) {
            bestStats.add(createBestStatItem("单场最高助攻",
                    String.valueOf(seasonStats.getBestAssists()), "次",
                    null, seasonStats.getBestAssistsGameId(),
                    "未知对手", "未知结果"));
        }

        if (seasonStats.getBestSteals() != null && seasonStats.getBestSteals() > 0) {
            bestStats.add(createBestStatItem("单场最高抢断",
                    String.valueOf(seasonStats.getBestSteals()), "次",
                    null, seasonStats.getBestStealsGameId(),
                    "未知对手", "未知结果"));
        }

        if (seasonStats.getBestBlocks() != null && seasonStats.getBestBlocks() > 0) {
            bestStats.add(createBestStatItem("单场最高盖帽",
                    String.valueOf(seasonStats.getBestBlocks()), "次",
                    null, seasonStats.getBestBlocksGameId(),
                    "未知对手", "未知结果"));
        }

        return bestStats;
    }

    /**
     * 构建真实的赛季选项列表
     */
    private List<AppPlayerSeasonOptionRespVO> buildRealSeasonOptions() {
        List<AppPlayerSeasonOptionRespVO> seasons = new ArrayList<>();

        // 获取当前年份
        int currentYear = LocalDate.now().getYear();

        // 生成最近3年的赛季选项
        for (int i = 0; i < 3; i++) {
            int year = currentYear - i;
            AppPlayerSeasonOptionRespVO season = new AppPlayerSeasonOptionRespVO();
            season.setName(year + "赛季");
            season.setValue(String.valueOf(year));
            season.setStatus(i == 0 ? "进行中" : "已结束");
            season.setDescription(year + "年度赛季");
            seasons.add(season);
        }

        return seasons;
    }

    /**
     * 构建球员球队经历
     */
    private List<AppPlayerTeamHistoryRespVO> buildPlayerTeamHistory(Long playerId) {
        List<AppPlayerTeamHistoryRespVO> teamHistory = new ArrayList<>();

        // 这里应该从球队合同表或相关表获取真实数据
        // 目前返回空列表，后续根据实际业务需求补充

        return teamHistory;
    }

    /**
     * 构建球员荣誉数据
     */
    private List<AppPlayerHonorRespVO> buildPlayerHonors(Long playerId) {
        List<AppPlayerHonorRespVO> honors = new ArrayList<>();

        // 这里应该从荣誉表获取真实数据
        // 目前返回空列表，后续根据实际业务需求补充

        return honors;
    }

    /**
     * 构建球员详细数据统计
     */
    private AppPlayerDataStatsRespVO buildPlayerDataStats(AppPlayerCareerQueryReqVO reqVO) {
        AppPlayerDataStatsRespVO response = new AppPlayerDataStatsRespVO();

        // 获取球员ID
        Long playerId = getPlayerIdFromRequest(reqVO);
        if (playerId == null) {
            return response;
        }

        // 获取生涯统计数据
        PlayerCareerStatsDO careerStats = playerCareerStatsService.calculateCareerStats(playerId, reqVO.getGameType());

        if (careerStats != null) {
            // 构建详细统计数据
            response.setTotalGames(careerStats.getGamesPlayed());
            response.setTotalPoints(careerStats.getTotalPoints());
            response.setTotalRebounds(careerStats.getTotalRebounds());
            response.setTotalAssists(careerStats.getTotalAssists());
            response.setAvgPoints(careerStats.getAvgPoints());
            response.setAvgRebounds(careerStats.getAvgRebounds());
            response.setAvgAssists(careerStats.getAvgAssists());
            response.setFieldGoalPercentage(careerStats.getFieldGoalPercentage());
            response.setThreePointPercentage(careerStats.getThreePointPercentage());
            response.setFreeThrowPercentage(careerStats.getFreeThrowPercentage());
        }

        return response;
    }

    /**
     * 构建球员比赛列表
     */
    private PageResult<AppPlayerMatchRespVO> buildPlayerMatchList(AppPlayerMatchPageReqVO pageReqVO) {
        // 获取球员ID
        Long playerId = getPlayerIdFromRequest(pageReqVO);
        if (playerId == null) {
            return PageResult.empty();
        }

        // 获取球员比赛列表
        List<AppPlayerMatchRespVO> matches = new ArrayList<>();

        // 这里应该从比赛表和统计表获取真实数据
        // 目前返回空列表，后续根据实际业务需求补充

        return new PageResult<>(matches, 0L);
    }

    // 辅助方法

    /**
     * 获取联盟最佳数据
     */
    private LeagueBestStats getLeagueBestStats(Integer gameType) {
        try {
            // 尝试从数据库计算真实的联盟最佳数据
            LeagueBestStats bestStats = calculateLeagueBestFromDatabase(gameType);
            if (bestStats != null) {
                log.debug("使用数据库计算的联盟最佳数据");
                return bestStats;
            }
        } catch (Exception e) {
            log.warn("获取联盟最佳数据失败，使用默认值: {}", e.getMessage());
        }

        // 如果查询失败或没有数据，使用合理的默认值
        log.debug("使用默认联盟最佳数据");
        LeagueBestStats defaultStats = new LeagueBestStats();
        defaultStats.setBestPoints(BigDecimal.valueOf(35.0));    // 联盟得分最佳
        defaultStats.setBestRebounds(BigDecimal.valueOf(15.0));  // 联盟篮板最佳
        defaultStats.setBestAssists(BigDecimal.valueOf(12.0));   // 联盟助攻最佳
        defaultStats.setBestBlocks(BigDecimal.valueOf(3.0));     // 联盟盖帽最佳
        defaultStats.setBestSteals(BigDecimal.valueOf(3.5));     // 联盟抢断最佳
        return defaultStats;
    }

    /**
     * 从聚合表计算联盟最佳数据（优化版本）
     */
    private LeagueBestStats calculateLeagueBestFromDatabase(Integer gameType) {
        try {
            // 直接从聚合统计表查询所有有效球员数据
            List<PlayerCareerStatsDO> allCareerStats = playerCareerStatsService.getAllValidCareerStats(gameType);

            if (allCareerStats.isEmpty()) {
                log.debug("聚合表中没有找到有效的球员统计数据，尝试使用原有方法");
                return calculateLeagueBestFromOriginalData(gameType);
            }

            // 过滤出有效数据的球员（至少参加过5场比赛）
            List<PlayerCareerStatsDO> validPlayers = allCareerStats.stream()
                    .filter(stats -> stats != null)
                    .filter(stats -> stats.getGamesPlayed() != null && stats.getGamesPlayed() >= 5)
                    .filter(stats -> stats.getAvgPoints() != null || stats.getAvgRebounds() != null || stats.getAvgAssists() != null)
                    .collect(Collectors.toList());

            if (validPlayers.isEmpty()) {
                log.debug("没有找到满足条件的球员统计数据");
                return null;
            }

            // 计算最佳值 - 直接使用场均数据（聚合表已计算好）
            LeagueBestStats bestStats = new LeagueBestStats();

            double bestPoints = validPlayers.stream()
                    .filter(stats -> stats.getAvgPoints() != null)
                    .mapToDouble(stats -> stats.getAvgPoints().doubleValue())
                    .filter(value -> value > 0)
                    .max().orElse(35.0);

            double bestRebounds = validPlayers.stream()
                    .filter(stats -> stats.getAvgRebounds() != null)
                    .mapToDouble(stats -> stats.getAvgRebounds().doubleValue())
                    .filter(value -> value > 0)
                    .max().orElse(15.0);

            double bestAssists = validPlayers.stream()
                    .filter(stats -> stats.getAvgAssists() != null)
                    .mapToDouble(stats -> stats.getAvgAssists().doubleValue())
                    .filter(value -> value > 0)
                    .max().orElse(12.0);

            double bestBlocks = validPlayers.stream()
                    .filter(stats -> stats.getAvgBlocks() != null)
                    .mapToDouble(stats -> stats.getAvgBlocks().doubleValue())
                    .filter(value -> value >= 0)
                    .max().orElse(3.0);

            double bestSteals = validPlayers.stream()
                    .filter(stats -> stats.getAvgSteals() != null)
                    .mapToDouble(stats -> stats.getAvgSteals().doubleValue())
                    .filter(value -> value >= 0)
                    .max().orElse(3.5);

            bestStats.setBestPoints(BigDecimal.valueOf(bestPoints).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestRebounds(BigDecimal.valueOf(bestRebounds).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestAssists(BigDecimal.valueOf(bestAssists).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestBlocks(BigDecimal.valueOf(bestBlocks).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestSteals(BigDecimal.valueOf(bestSteals).setScale(2, RoundingMode.HALF_UP));

            log.debug("✅ 从聚合表计算联盟最佳数据 - 得分:{}, 篮板:{}, 助攻:{}, 盖帽:{}, 抢断:{}, 样本数:{}",
                    bestPoints, bestRebounds, bestAssists, bestBlocks, bestSteals, validPlayers.size());

            return bestStats;
        } catch (Exception e) {
            log.error("从聚合表计算联盟最佳数据失败，尝试使用原有方法", e);
            return calculateLeagueBestFromOriginalData(gameType);
        }
    }

    /**
     * 从原始数据计算联盟最佳数据（备用方法）
     */
    private LeagueBestStats calculateLeagueBestFromOriginalData(Integer gameType) {
        try {
            // 获取所有有效球员的生涯统计数据
            Map<Long, PlayerCareerStatistics> allPlayerStats = playerStatisticsService.getPlayerCareerStatisticsMap();

            if (allPlayerStats.isEmpty()) {
                return null;
            }

            // 过滤出有效数据的球员（至少参加过5场比赛，且有基础统计数据）
            List<PlayerCareerStatistics> validPlayers = allPlayerStats.values().stream()
                    .filter(stats -> stats != null)
                    .filter(stats -> stats.getTotalGames() != null && stats.getTotalGames() >= 5)
                    .filter(stats -> stats.getBasicStatistics() != null)
                    .collect(Collectors.toList());

            if (validPlayers.isEmpty()) {
                log.debug("没有找到有效的球员统计数据");
                return null;
            }

            // 计算最佳值 - 找出每个维度的最高场均数据
            LeagueBestStats bestStats = new LeagueBestStats();

            double bestPoints = validPlayers.stream()
                    .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getPoints(), stats.getTotalGames()))
                    .filter(value -> value > 0)
                    .max().orElse(35.0);

            double bestRebounds = validPlayers.stream()
                    .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getRebounds(), stats.getTotalGames()))
                    .filter(value -> value > 0)
                    .max().orElse(15.0);

            double bestAssists = validPlayers.stream()
                    .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getAssists(), stats.getTotalGames()))
                    .filter(value -> value > 0)
                    .max().orElse(12.0);

            double bestBlocks = validPlayers.stream()
                    .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getBlocks(), stats.getTotalGames()))
                    .filter(value -> value >= 0)
                    .max().orElse(3.0);

            double bestSteals = validPlayers.stream()
                    .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getSteals(), stats.getTotalGames()))
                    .filter(value -> value >= 0)
                    .max().orElse(3.5);

            bestStats.setBestPoints(BigDecimal.valueOf(bestPoints).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestRebounds(BigDecimal.valueOf(bestRebounds).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestAssists(BigDecimal.valueOf(bestAssists).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestBlocks(BigDecimal.valueOf(bestBlocks).setScale(2, RoundingMode.HALF_UP));
            bestStats.setBestSteals(BigDecimal.valueOf(bestSteals).setScale(2, RoundingMode.HALF_UP));

            log.debug("📊 从原始数据计算联盟最佳数据 - 得分:{}, 篮板:{}, 助攻:{}, 盖帽:{}, 抢断:{}, 样本数:{}",
                    bestPoints, bestRebounds, bestAssists, bestBlocks, bestSteals, validPlayers.size());

            return bestStats;
        } catch (Exception e) {
            log.error("从原始数据计算联盟最佳数据失败", e);
            return null;
        }
    }

    /**
     * 计算相对于联盟最佳值的评分
     *
     * @param playerValue 球员数据
     * @param leagueBest  联盟最佳值
     * @return 相对评分，100表示达到最佳水平
     */
    private BigDecimal calculateBestComparisonScore(BigDecimal playerValue, BigDecimal leagueBest) {
        if (playerValue == null || leagueBest == null || leagueBest.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(50); // 默认中等水平
        }

        // 计算相对于最佳球员的百分比，100表示达到最佳水平
        double percentage = playerValue.divide(leagueBest, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();

        // 限制在合理范围内（最低0，最高100）
        percentage = Math.min(100, Math.max(0, percentage));

        return BigDecimal.valueOf(percentage).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算球员单项数据的场均值
     */
    private double calculatePlayerAverage(Integer totalValue, Integer totalGames) {
        if (totalValue == null || totalGames == null || totalGames <= 0) {
            return 0.0;
        }
        return (double) totalValue / totalGames;
    }

    /**
     * 从请求中获取球员ID
     */
    private Long getPlayerIdFromRequest(AppPlayerCareerQueryReqVO reqVO) {
        if (reqVO.getPlayerId() != null) {
            return reqVO.getPlayerId();
        }

        try {
            Long userId = getLoginUserId();
            PlayerDO player = playerService.getPlayerByUserId(userId);
            return player != null ? player.getId() : null;
        } catch (Exception e) {
            log.warn("获取当前用户球员ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从请求中获取球员ID
     */
    private Long getPlayerIdFromRequest(AppPlayerMatchPageReqVO reqVO) {
        if (reqVO.getPlayerId() != null) {
            return reqVO.getPlayerId();
        }

        try {
            Long userId = getLoginUserId();
            PlayerDO player = playerService.getPlayerByUserId(userId);
            return player != null ? player.getId() : null;
        } catch (Exception e) {
            log.warn("获取当前用户球员ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前赛季
     */
    private String getCurrentSeason() {
        return String.valueOf(LocalDate.now().getYear());
    }

    /**
     * 构建可用赛季列表
     */
    private List<AppPlayerCareerOverviewRespVO.SeasonOption> buildAvailableSeasons(String currentSeason) {
        List<AppPlayerCareerOverviewRespVO.SeasonOption> seasons = new ArrayList<>();

        int currentYear = LocalDate.now().getYear();
        for (int i = 0; i < 3; i++) {
            int year = currentYear - i;
            AppPlayerCareerOverviewRespVO.SeasonOption season = new AppPlayerCareerOverviewRespVO.SeasonOption();
            season.setName(year + "赛季");
            season.setValue(String.valueOf(year));
            season.setCurrent(String.valueOf(year).equals(currentSeason));
            season.setStatus(i == 0 ? "进行中" : "已结束");
            season.setDescription(year + "年度赛季");
            seasons.add(season);
        }

        return seasons;
    }

    /**
     * 创建统计项
     */
    private AppPlayerCareerOverviewRespVO.StatItem createStatItem(String label, String value, String unit, String valueType) {
        AppPlayerCareerOverviewRespVO.StatItem item = new AppPlayerCareerOverviewRespVO.StatItem();
        item.setLabel(label);
        item.setValue(value);
        item.setUnit(unit);
        item.setValueType(valueType);
        return item;
    }

    /**
     * 创建雷达图数据点
     */
    private AppPlayerCareerOverviewRespVO.RadarDataPoint createRadarDataPoint(String dimension, BigDecimal score, BigDecimal maxValue) {
        AppPlayerCareerOverviewRespVO.RadarDataPoint point = new AppPlayerCareerOverviewRespVO.RadarDataPoint();
        point.setDimension(dimension);
        point.setScore(score);
        point.setMaxValue(maxValue);
        return point;
    }

    /**
     * 创建带描述的雷达图数据点
     */
    private AppPlayerCareerOverviewRespVO.RadarDataPoint createRadarDataPointWithDescription(String dimension, BigDecimal score, BigDecimal maxValue, String description) {
        AppPlayerCareerOverviewRespVO.RadarDataPoint point = new AppPlayerCareerOverviewRespVO.RadarDataPoint();
        point.setDimension(dimension);
        point.setScore(score);
        point.setMaxValue(maxValue);
        point.setDescription(description);
        return point;
    }

    /**
     * 创建最佳统计项
     */
    private AppPlayerBestStatsRespVO.BestStatItem createBestStatItem(String statName, String value, String unit,
                                                                     LocalDate achieveDate, Long gameId, String opponent, String gameResult) {
        AppPlayerBestStatsRespVO.BestStatItem item = new AppPlayerBestStatsRespVO.BestStatItem();
        item.setStatName(statName);
        item.setValue(value);
        item.setUnit(unit);
        item.setAchieveDate(achieveDate);
        item.setGameId(gameId);
        item.setOpponent(opponent);
        item.setGameResult(gameResult);
        return item;
    }

    // 评分计算方法

    /**
     * 计算效率评分
     */
    private BigDecimal calculateEfficiencyScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgEfficiency() == null) {
            return BigDecimal.valueOf(60);
        }
        // 基于效率值计算评分，效率值20对应80分
        double score = Math.min(100, Math.max(0, careerStats.getAvgEfficiency().doubleValue() * 4));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算得分评分
     */
    private BigDecimal calculateScoringScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgPoints() == null) {
            return BigDecimal.valueOf(60);
        }
        // 基于场均得分计算评分，场均20分对应80分
        double score = Math.min(100, Math.max(0, careerStats.getAvgPoints().doubleValue() * 4));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算篮板评分
     */
    private BigDecimal calculateReboundingScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgRebounds() == null) {
            return BigDecimal.valueOf(60);
        }
        // 基于场均篮板计算评分，场均10个对应80分
        double score = Math.min(100, Math.max(0, careerStats.getAvgRebounds().doubleValue() * 8));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算助攻评分
     */
    private BigDecimal calculateAssistScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgAssists() == null) {
            return BigDecimal.valueOf(60);
        }
        // 基于场均助攻计算评分，场均8次对应80分
        double score = Math.min(100, Math.max(0, careerStats.getAvgAssists().doubleValue() * 10));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算防守评分
     */
    private BigDecimal calculateDefenseScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgSteals() == null || careerStats.getAvgBlocks() == null) {
            return BigDecimal.valueOf(60);
        }
        // 基于抢断和盖帽计算防守评分
        double defenseValue = careerStats.getAvgSteals().doubleValue() + careerStats.getAvgBlocks().doubleValue();
        double score = Math.min(100, Math.max(0, defenseValue * 20));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算失误控制评分
     */
    private BigDecimal calculateTurnoverScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgTurnovers() == null) {
            return BigDecimal.valueOf(60);
        }
        // 失误越少评分越高，场均3次失误对应60分
        double score = Math.min(100, Math.max(0, 100 - careerStats.getAvgTurnovers().doubleValue() * 20));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算犯规控制评分
     */
    private BigDecimal calculateFoulScore(PlayerCareerStatsDO careerStats) {
        if (careerStats.getAvgFouls() == null) {
            return BigDecimal.valueOf(60);
        }
        // 犯规越少评分越高，场均4次犯规对应60分
        double score = Math.min(100, Math.max(0, 100 - careerStats.getAvgFouls().doubleValue() * 15));
        return BigDecimal.valueOf(score).setScale(1, RoundingMode.HALF_UP);
    }

    // 格式化方法

    /**
     * 格式化小数
     */
    private String formatDecimal(BigDecimal value) {
        if (value == null) {
            return "0.0";
        }
        return value.setScale(1, RoundingMode.HALF_UP).toString();
    }

    /**
     * 格式化百分比 - 智能处理不同格式的百分比数据
     */
    private String formatPercentage(BigDecimal value) {
        if (value == null) {
            return "0.0";
        }

        // 如果值大于1，说明已经是百分比格式（如41.83表示41.83%）
        // 如果值小于等于1，说明是小数格式（如0.4183表示41.83%）
        if (value.compareTo(BigDecimal.ONE) > 0) {
            // 已经是百分比格式，直接格式化
            return value.setScale(1, RoundingMode.HALF_UP).toString();
        } else {
            // 小数格式，需要乘以100
            return value.multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP).toString();
        }
    }

    // 默认数据创建方法

    /**
     * 创建默认基础统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultBasicStats() {
        return Arrays.asList(
                createStatItem("场均得分", "0.0", "分", "number"),
                createStatItem("场均篮板", "0.0", "个", "number"),
                createStatItem("场均助攻", "0.0", "次", "number"),
                createStatItem("场均抢断", "0.0", "次", "number"),
                createStatItem("场均盖帽", "0.0", "次", "number"),
                createStatItem("场均失误", "0.0", "次", "number")
        );
    }

    /**
     * 创建默认命中率统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultShootingStats() {
        return Arrays.asList(
                createStatItem("投篮命中率", "0.0", "%", "percentage"),
                createStatItem("三分命中率", "0.0", "%", "percentage"),
                createStatItem("二分命中率", "0.0", "%", "percentage"),
                createStatItem("罚球命中率", "0.0", "%", "percentage")
        );
    }

    /**
     * 创建默认高阶统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultAdvancedStats() {
        return Arrays.asList(
                createStatItem("效率值", "0.0", "", "number"),
                createStatItem("真实命中率", "0.0", "%", "percentage"),
                createStatItem("使用率", "0.0", "%", "percentage"),
                createStatItem("净胜分", "0.0", "", "number")
        );
    }

    /**
     * 创建默认连胜统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultStreakStats() {
        return Arrays.asList(
                createStatItem("当前连胜", "0", "场", "number"),
                createStatItem("最大连胜", "0", "场", "number"),
                createStatItem("生涯最大连胜", "0", "场", "number"),
                createStatItem("胜率", "0.0", "%", "percentage")
        );
    }

    /**
     * 创建默认雷达图数据 - 与最佳球员对比版本
     */
    private List<AppPlayerCareerOverviewRespVO.RadarDataPoint> createDefaultRadarDataForBestComparison() {
        return Arrays.asList(
                createRadarDataPoint("得分", BigDecimal.valueOf(50), BigDecimal.valueOf(100)),
                createRadarDataPoint("篮板", BigDecimal.valueOf(50), BigDecimal.valueOf(100)),
                createRadarDataPoint("助攻", BigDecimal.valueOf(50), BigDecimal.valueOf(100)),
                createRadarDataPoint("盖帽", BigDecimal.valueOf(50), BigDecimal.valueOf(100)),
                createRadarDataPoint("抢断", BigDecimal.valueOf(50), BigDecimal.valueOf(100))
        );
    }

    /**
     * 联盟最佳数据内部类
     */
    @Data
    public static class LeagueBestStats {
        private BigDecimal bestPoints;
        private BigDecimal bestRebounds;
        private BigDecimal bestAssists;
        private BigDecimal bestBlocks;
        private BigDecimal bestSteals;
    }
}