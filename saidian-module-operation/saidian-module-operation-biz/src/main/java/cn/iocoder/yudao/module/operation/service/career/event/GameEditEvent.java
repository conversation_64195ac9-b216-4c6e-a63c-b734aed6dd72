package cn.iocoder.yudao.module.operation.service.career.event;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 比赛编辑事件
 * 
 * 当比赛基础信息（如胜负、时间等）被编辑时发布此事件，
 * 只更新胜负相关的统计数据，不涉及详细技术统计
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
public class GameEditEvent extends ApplicationEvent {

    private Long gameId;
    private List<Long> playerIds; // 参与比赛的球员ID列表
    private String updateType; // 更新类型：result（结果变更）、time（时间变更）等
    private LocalDateTime gameTime;

    public GameEditEvent(Object source, Long gameId, List<Long> playerIds, String updateType, LocalDateTime gameTime) {
        super(source);
        this.gameId = gameId;
        this.playerIds = playerIds;
        this.updateType = updateType;
        this.gameTime = gameTime;
    }

    /**
     * 创建比赛结果变更事件
     */
    public static GameEditEvent createResultChangeEvent(Object source, Long gameId, List<Long> playerIds, LocalDateTime gameTime) {
        return new GameEditEvent(source, gameId, playerIds, "result", gameTime);
    }

    /**
     * 创建比赛时间变更事件
     */
    public static GameEditEvent createTimeChangeEvent(Object source, Long gameId, List<Long> playerIds, LocalDateTime gameTime) {
        return new GameEditEvent(source, gameId, playerIds, "time", gameTime);
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public List<Long> getPlayerIds() {
        return playerIds;
    }

    public void setPlayerIds(List<Long> playerIds) {
        this.playerIds = playerIds;
    }

    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    public LocalDateTime getGameTime() {
        return gameTime;
    }

    public void setGameTime(LocalDateTime gameTime) {
        this.gameTime = gameTime;
    }
}