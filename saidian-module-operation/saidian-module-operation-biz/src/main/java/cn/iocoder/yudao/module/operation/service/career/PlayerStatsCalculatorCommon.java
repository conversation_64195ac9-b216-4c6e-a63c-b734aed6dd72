package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;

/**
 * 球员统计计算器通用组件
 * 
 * 提供赛季和生涯统计计算的公共逻辑，避免代码重复
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class PlayerStatsCalculatorCommon {

    /**
     * 统计数据聚合结果
     */
    @Setter
    @Getter
    public static class StatsAggregationResult {
        // Getters and Setters
        // 基础统计
        private int totalGames;
        private int validGames;
        private int totalPoints;
        private int totalRebounds;
        private int totalOffensiveRebounds;
        private int totalDefensiveRebounds;
        private int totalAssists;
        private int totalSteals;
        private int totalBlocks;
        private int totalTurnovers;
        private int totalFouls;
        private int totalFieldGoalsMade;
        private int totalFieldGoalsAttempted;
        private int totalThreePointsMade;
        private int totalThreePointsAttempted;
        private int totalTwoPointsMade;
        private int totalTwoPointsAttempted;
        private int totalFreeThrowsMade;
        private int totalFreeThrowsAttempted;
        private BigDecimal totalMinutes;
        private BigDecimal totalEfficiency;
        
        // 平均统计
        private BigDecimal avgPoints;
        private BigDecimal avgRebounds;
        private BigDecimal avgOffensiveRebounds;
        private BigDecimal avgDefensiveRebounds;
        private BigDecimal avgAssists;
        private BigDecimal avgSteals;
        private BigDecimal avgBlocks;
        private BigDecimal avgTurnovers;
        private BigDecimal avgFouls;
        private BigDecimal avgMinutes;
        private BigDecimal avgEfficiency;
        
        // 命中率统计
        private BigDecimal fieldGoalPercentage;
        private BigDecimal threePointPercentage;
        private BigDecimal twoPointPercentage;
        private BigDecimal freeThrowPercentage;
        private BigDecimal trueShootingPercentage;
        private BigDecimal assistTurnoverRatio;

    }

    /**
     * 连胜数据结果
     */
    public static class StreakDataResult {
        private int currentStreak;
        private int maxWinStreak;
        private int maxLoseStreak;
        private LocalDate streakStartDate;
        private int totalWins;
        private int totalLosses;
        private BigDecimal winRate;
        
        // Getters and Setters
        public int getCurrentStreak() { return currentStreak; }
        public void setCurrentStreak(int currentStreak) { this.currentStreak = currentStreak; }
        
        public int getMaxWinStreak() { return maxWinStreak; }
        public void setMaxWinStreak(int maxWinStreak) { this.maxWinStreak = maxWinStreak; }
        
        public int getMaxLoseStreak() { return maxLoseStreak; }
        public void setMaxLoseStreak(int maxLoseStreak) { this.maxLoseStreak = maxLoseStreak; }
        
        public LocalDate getStreakStartDate() { return streakStartDate; }
        public void setStreakStartDate(LocalDate streakStartDate) { this.streakStartDate = streakStartDate; }
        
        public int getTotalWins() { return totalWins; }
        public void setTotalWins(int totalWins) { this.totalWins = totalWins; }
        
        public int getTotalLosses() { return totalLosses; }
        public void setTotalLosses(int totalLosses) { this.totalLosses = totalLosses; }
        
        public BigDecimal getWinRate() { return winRate; }
        public void setWinRate(BigDecimal winRate) { this.winRate = winRate; }
    }

    /**
     * 聚合基础统计数据
     * 
     * @param gameStats 比赛统计数据列表
     * @return 聚合结果
     */
    public StatsAggregationResult aggregateStats(List<PlayerStatisticsDO> gameStats) {
        if (gameStats == null || gameStats.isEmpty()) {
            return createEmptyAggregationResult();
        }

        StatsAggregationResult result = new StatsAggregationResult();
        
        // 初始化累计值
        int totalGames = gameStats.size();
        int validGames = 0;
        int totalPoints = 0;
        int totalOffensiveRebounds = 0;
        int totalDefensiveRebounds = 0;
        int totalAssists = 0;
        int totalSteals = 0;
        int totalBlocks = 0;
        int totalTurnovers = 0;
        int totalFouls = 0;
        int totalTwoPointsMade = 0;
        int totalTwoPointsAttempted = 0;
        int totalThreePointsMade = 0;
        int totalThreePointsAttempted = 0;
        int totalFreeThrowsMade = 0;
        int totalFreeThrowsAttempted = 0;
        BigDecimal totalMinutes = BigDecimal.ZERO;
        BigDecimal totalEfficiency = BigDecimal.ZERO;

        // 遍历所有比赛数据进行累加
        for (PlayerStatisticsDO gameStat : gameStats) {
            // 修复：每场比赛都应该计入总场次，不管是否有得分数据
            totalGames++;

            if (gameStat.getPoints() != null) {
                totalPoints += gameStat.getPoints();
                validGames++; // 有得分数据的比赛数（保留用于其他统计）
                log.debug("📈 累加比赛数据 - 比赛ID: {}, 得分: {}, 累计得分: {}, 总场次: {}, 有效场次: {}",
                        gameStat.getGameId(), gameStat.getPoints(), totalPoints, totalGames, validGames);
            } else {
                log.debug("⚠️ 无得分数据的比赛 - 比赛ID: {}, 总场次: {}", gameStat.getGameId(), totalGames);
            }
            
            if (gameStat.getOffensiveRebounds() != null) {
                totalOffensiveRebounds += gameStat.getOffensiveRebounds();
            }
            if (gameStat.getDefensiveRebounds() != null) {
                totalDefensiveRebounds += gameStat.getDefensiveRebounds();
            }
            if (gameStat.getAssists() != null) {
                totalAssists += gameStat.getAssists();
            }
            if (gameStat.getSteals() != null) {
                totalSteals += gameStat.getSteals();
            }
            if (gameStat.getBlocks() != null) {
                totalBlocks += gameStat.getBlocks();
            }
            if (gameStat.getTurnovers() != null) {
                totalTurnovers += gameStat.getTurnovers();
            }
            if (gameStat.getFouls() != null) {
                totalFouls += gameStat.getFouls();
            }
            if (gameStat.getTwoPointMakes() != null) {
                totalTwoPointsMade += gameStat.getTwoPointMakes();
            }
            if (gameStat.getTwoPointAttempts() != null) {
                totalTwoPointsAttempted += gameStat.getTwoPointAttempts();
            }
            if (gameStat.getThreePointMakes() != null) {
                totalThreePointsMade += gameStat.getThreePointMakes();
            }
            if (gameStat.getThreePointAttempts() != null) {
                totalThreePointsAttempted += gameStat.getThreePointAttempts();
            }
            if (gameStat.getFreeThrowMakes() != null) {
                totalFreeThrowsMade += gameStat.getFreeThrowMakes();
            }
            if (gameStat.getFreeThrowAttempts() != null) {
                totalFreeThrowsAttempted += gameStat.getFreeThrowAttempts();
            }
            
            // 上场时间转换为分钟
            if (gameStat.getPlayingTime() != null) {
                BigDecimal minutes = BigDecimal.valueOf(gameStat.getPlayingTime()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                totalMinutes = totalMinutes.add(minutes);
            }
            
            if (gameStat.getEfficiency() != null) {
                totalEfficiency = totalEfficiency.add(BigDecimal.valueOf(gameStat.getEfficiency()));
            }
        }

        // 计算总投篮数据
        int totalFieldGoalsMade = totalTwoPointsMade + totalThreePointsMade;
        int totalFieldGoalsAttempted = totalTwoPointsAttempted + totalThreePointsAttempted;
        int totalRebounds = totalOffensiveRebounds + totalDefensiveRebounds;

        // 设置总计数据
        result.setTotalGames(totalGames);
        result.setValidGames(validGames);
        result.setTotalPoints(totalPoints);
        result.setTotalRebounds(totalRebounds);
        result.setTotalOffensiveRebounds(totalOffensiveRebounds);
        result.setTotalDefensiveRebounds(totalDefensiveRebounds);
        result.setTotalAssists(totalAssists);
        result.setTotalSteals(totalSteals);
        result.setTotalBlocks(totalBlocks);
        result.setTotalTurnovers(totalTurnovers);
        result.setTotalFouls(totalFouls);
        result.setTotalFieldGoalsMade(totalFieldGoalsMade);
        result.setTotalFieldGoalsAttempted(totalFieldGoalsAttempted);
        result.setTotalTwoPointsMade(totalTwoPointsMade);
        result.setTotalTwoPointsAttempted(totalTwoPointsAttempted);
        result.setTotalThreePointsMade(totalThreePointsMade);
        result.setTotalThreePointsAttempted(totalThreePointsAttempted);
        result.setTotalFreeThrowsMade(totalFreeThrowsMade);
        result.setTotalFreeThrowsAttempted(totalFreeThrowsAttempted);
        result.setTotalMinutes(totalMinutes);
        result.setTotalEfficiency(totalEfficiency);

        // 计算平均数据
        calculateAverageStats(result);
        
        // 计算命中率
        calculateShootingPercentages(result);
        
        // 计算高阶统计
        calculateAdvancedStats(result);

        return result;
    }

    /**
     * 计算连胜数据
     * 
     * @param gameResults 比赛结果列表
     * @return 连胜数据结果
     */
    public StreakDataResult calculateStreakData(List<PlayerGameRelatedDO> gameResults) {
        StreakDataResult result = new StreakDataResult();
        
        if (gameResults == null || gameResults.isEmpty()) {
            // 设置默认值
            result.setCurrentStreak(0);
            result.setMaxWinStreak(0);
            result.setMaxLoseStreak(0);
            result.setTotalWins(0);
            result.setTotalLosses(0);
            result.setWinRate(BigDecimal.ZERO);
            return result;
        }

        int currentStreak = 0;
        int maxWinStreak = 0;
        int maxLoseStreak = 0;
        int totalWins = 0;
        int totalLosses = 0;
        LocalDate streakStartDate = null;
        int tempWinStreak = 0;
        int tempLoseStreak = 0;

        // 遍历比赛结果计算连胜数据
        for (PlayerGameRelatedDO gameResult : gameResults) {
            // TODO: 实际实现需要根据比赛结果判断胜负
            // 这里暂时使用简化逻辑
            boolean isWin = determineGameResult(gameResult);
            
            if (isWin) {
                totalWins++;
                tempWinStreak++;
                tempLoseStreak = 0;
                maxWinStreak = Math.max(maxWinStreak, tempWinStreak);
                currentStreak = tempWinStreak;
                if (tempWinStreak == 1) {
                    streakStartDate = gameResult.getCreateTime().toLocalDate();
                }
            } else {
                totalLosses++;
                tempLoseStreak++;
                tempWinStreak = 0;
                maxLoseStreak = Math.max(maxLoseStreak, tempLoseStreak);
                currentStreak = -tempLoseStreak; // 负数表示连败
                if (tempLoseStreak == 1) {
                    streakStartDate = gameResult.getCreateTime().toLocalDate();
                }
            }
        }

        // 设置连胜数据
        result.setCurrentStreak(currentStreak);
        result.setMaxWinStreak(maxWinStreak);
        result.setMaxLoseStreak(maxLoseStreak);
        result.setStreakStartDate(streakStartDate);
        result.setTotalWins(totalWins);
        result.setTotalLosses(totalLosses);

        // 计算胜率 - 存储为小数格式（0-1），前端显示时再乘以100
        int totalGames = totalWins + totalLosses;
        if (totalGames > 0) {
            BigDecimal winRate = BigDecimal.valueOf(totalWins)
                    .divide(BigDecimal.valueOf(totalGames), 4, RoundingMode.HALF_UP);
            result.setWinRate(winRate);
        } else {
            result.setWinRate(BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 计算平均统计
     * 修复：使用totalGames而不是validGames，与数据初始化器保持一致
     */
    private void calculateAverageStats(StatsAggregationResult result) {
        int validGames = result.getValidGames();
        int totalGames = result.getTotalGames();

        log.debug("🧮 计算平均统计 - 总场次: {}, 有效场次: {}, 总得分: {}",
                totalGames, validGames, result.getTotalPoints());

        // 修复：使用totalGames而不是validGames来计算平均值，与PlayerCareerDataInitializerV2保持一致
        if (totalGames == 0) {
            log.warn("⚠️ 总场次为0，设置默认平均值");
            // 设置默认值
            result.setAvgPoints(BigDecimal.ZERO);
            result.setAvgRebounds(BigDecimal.ZERO);
            result.setAvgOffensiveRebounds(BigDecimal.ZERO);
            result.setAvgDefensiveRebounds(BigDecimal.ZERO);
            result.setAvgAssists(BigDecimal.ZERO);
            result.setAvgSteals(BigDecimal.ZERO);
            result.setAvgBlocks(BigDecimal.ZERO);
            result.setAvgTurnovers(BigDecimal.ZERO);
            result.setAvgFouls(BigDecimal.ZERO);
            result.setAvgMinutes(BigDecimal.ZERO);
            result.setAvgEfficiency(BigDecimal.ZERO);
            return;
        }

        BigDecimal avgPoints = BigDecimal.valueOf(result.getTotalPoints()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP);
        log.info("📊 场均得分计算修复: 总得分={}, 总场次={}, 场均得分={} (之前用有效场次{}会导致不一致)",
                result.getTotalPoints(), totalGames, avgPoints, validGames);

        result.setAvgPoints(avgPoints);
        result.setAvgRebounds(BigDecimal.valueOf(result.getTotalRebounds()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgOffensiveRebounds(BigDecimal.valueOf(result.getTotalOffensiveRebounds()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgDefensiveRebounds(BigDecimal.valueOf(result.getTotalDefensiveRebounds()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgAssists(BigDecimal.valueOf(result.getTotalAssists()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgSteals(BigDecimal.valueOf(result.getTotalSteals()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgBlocks(BigDecimal.valueOf(result.getTotalBlocks()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgTurnovers(BigDecimal.valueOf(result.getTotalTurnovers()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgFouls(BigDecimal.valueOf(result.getTotalFouls()).divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgMinutes(result.getTotalMinutes().divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
        result.setAvgEfficiency(result.getTotalEfficiency().divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
    }

    /**
     * 计算命中率 - 存储为小数格式（0-1），前端显示时再乘以100
     */
    private void calculateShootingPercentages(StatsAggregationResult result) {
        // 总投篮命中率
        if (result.getTotalFieldGoalsAttempted() > 0) {
            BigDecimal fgPercentage = BigDecimal.valueOf(result.getTotalFieldGoalsMade())
                    .divide(BigDecimal.valueOf(result.getTotalFieldGoalsAttempted()), 4, RoundingMode.HALF_UP);
            result.setFieldGoalPercentage(fgPercentage);
        } else {
            result.setFieldGoalPercentage(BigDecimal.ZERO);
        }

        // 三分命中率
        if (result.getTotalThreePointsAttempted() > 0) {
            BigDecimal threePointPercentage = BigDecimal.valueOf(result.getTotalThreePointsMade())
                    .divide(BigDecimal.valueOf(result.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP);
            result.setThreePointPercentage(threePointPercentage);
        } else {
            result.setThreePointPercentage(BigDecimal.ZERO);
        }

        // 二分命中率
        if (result.getTotalTwoPointsAttempted() > 0) {
            BigDecimal twoPointPercentage = BigDecimal.valueOf(result.getTotalTwoPointsMade())
                    .divide(BigDecimal.valueOf(result.getTotalTwoPointsAttempted()), 4, RoundingMode.HALF_UP);
            result.setTwoPointPercentage(twoPointPercentage);
        } else {
            result.setTwoPointPercentage(BigDecimal.ZERO);
        }

        // 罚球命中率
        if (result.getTotalFreeThrowsAttempted() > 0) {
            BigDecimal ftPercentage = BigDecimal.valueOf(result.getTotalFreeThrowsMade())
                    .divide(BigDecimal.valueOf(result.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP);
            result.setFreeThrowPercentage(ftPercentage);
        } else {
            result.setFreeThrowPercentage(BigDecimal.ZERO);
        }
    }

    /**
     * 计算高阶统计
     */
    private void calculateAdvancedStats(StatsAggregationResult result) {
        // 真实命中率 (TS%) = Points / (2 * (FGA + 0.44 * FTA))
        int totalFGA = result.getTotalFieldGoalsAttempted();
        int totalFTA = result.getTotalFreeThrowsAttempted();
        int totalPoints = result.getTotalPoints();
        
        if (totalFGA > 0 || totalFTA > 0) {
            double denominator = 2 * (totalFGA + 0.44 * totalFTA);
            if (denominator > 0) {
                BigDecimal tsPercentage = BigDecimal.valueOf(totalPoints)
                        .divide(BigDecimal.valueOf(denominator), 4, RoundingMode.HALF_UP);
                result.setTrueShootingPercentage(tsPercentage);
                log.debug("计算真实命中率 - 得分: {}, 总投篮: {}, 罚球: {}, TS%: {}", 
                        totalPoints, totalFGA, totalFTA, tsPercentage);
            } else {
                result.setTrueShootingPercentage(BigDecimal.ZERO);
                log.warn("真实命中率计算异常 - 分母为0");
            }
        } else {
            result.setTrueShootingPercentage(BigDecimal.ZERO);
        }

        // 助攻失误比
        if (result.getTotalTurnovers() > 0) {
            BigDecimal assistTurnoverRatio = BigDecimal.valueOf(result.getTotalAssists())
                    .divide(BigDecimal.valueOf(result.getTotalTurnovers()), 2, RoundingMode.HALF_UP);
            result.setAssistTurnoverRatio(assistTurnoverRatio);
        } else {
            result.setAssistTurnoverRatio(BigDecimal.ZERO);
        }
    }

    /**
     * 创建空的聚合结果
     */
    private StatsAggregationResult createEmptyAggregationResult() {
        StatsAggregationResult result = new StatsAggregationResult();
        result.setTotalGames(0);
        result.setValidGames(0);
        result.setTotalPoints(0);
        result.setTotalRebounds(0);
        result.setTotalOffensiveRebounds(0);
        result.setTotalDefensiveRebounds(0);
        result.setTotalAssists(0);
        result.setTotalSteals(0);
        result.setTotalBlocks(0);
        result.setTotalTurnovers(0);
        result.setTotalFouls(0);
        result.setTotalFieldGoalsMade(0);
        result.setTotalFieldGoalsAttempted(0);
        result.setTotalTwoPointsMade(0);
        result.setTotalTwoPointsAttempted(0);
        result.setTotalThreePointsMade(0);
        result.setTotalThreePointsAttempted(0);
        result.setTotalFreeThrowsMade(0);
        result.setTotalFreeThrowsAttempted(0);
        result.setTotalMinutes(BigDecimal.ZERO);
        result.setTotalEfficiency(BigDecimal.ZERO);
        
        // 设置默认的平均值和百分比
        result.setAvgPoints(BigDecimal.ZERO);
        result.setAvgRebounds(BigDecimal.ZERO);
        result.setAvgOffensiveRebounds(BigDecimal.ZERO);
        result.setAvgDefensiveRebounds(BigDecimal.ZERO);
        result.setAvgAssists(BigDecimal.ZERO);
        result.setAvgSteals(BigDecimal.ZERO);
        result.setAvgBlocks(BigDecimal.ZERO);
        result.setAvgTurnovers(BigDecimal.ZERO);
        result.setAvgFouls(BigDecimal.ZERO);
        result.setAvgMinutes(BigDecimal.ZERO);
        result.setAvgEfficiency(BigDecimal.ZERO);
        result.setFieldGoalPercentage(BigDecimal.ZERO);
        result.setThreePointPercentage(BigDecimal.ZERO);
        result.setTwoPointPercentage(BigDecimal.ZERO);
        result.setFreeThrowPercentage(BigDecimal.ZERO);
        result.setTrueShootingPercentage(BigDecimal.ZERO);
        result.setAssistTurnoverRatio(BigDecimal.ZERO);
        
        return result;
    }

    /**
     * 判断比赛结果（简化实现）
     */
    private boolean determineGameResult(PlayerGameRelatedDO gameResult) {
        // TODO: 实际实现需要根据比赛结果和球队得分判断胜负
        // 这里暂时返回随机结果
        return gameResult.getId() % 2 == 0;
    }
} 