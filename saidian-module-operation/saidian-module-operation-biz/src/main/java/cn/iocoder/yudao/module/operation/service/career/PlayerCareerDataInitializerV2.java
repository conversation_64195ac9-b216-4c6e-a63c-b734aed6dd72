package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerSeasonStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerBestStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.SeasonDO;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.GameMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerSeasonStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerBestStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.service.player.SeasonService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 球员生涯数据初始化器 V2 - 适配新的分离存储表结构
 *
 * 使用新的分离存储策略：
 * - PlayerSeasonStatsDO: 赛季统计数据
 * - PlayerCareerStatsDO: 生涯统计数据  
 * - PlayerCareerBestStatsDO: 生涯最佳数据
 *
 * <AUTHOR> Assistant
 * @since 2024
 */
@Service
@Slf4j
public class PlayerCareerDataInitializerV2 {

    @Resource
    private PlayerMapper playerMapper;
    @Resource
    private PlayerStatisticsMapper playerStatisticsMapper;
    @Resource
    private GameMapper gameMapper;
    @Resource
    private PlayerSeasonStatsMapper playerSeasonStatsMapper;
    @Resource
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    @Resource
    private PlayerCareerBestStatsMapper playerCareerBestStatsMapper;
    @Resource
    private PlayerGameRelatedMapper playerGameRelatedMapper;
    @Resource
    private SeasonService seasonService;

    // 常量定义
    private static final int STATS_SECTION_FULL_GAME = 0;
    
    // 比赛类型常量
    private static final int GAME_TYPE_ALL = 0; // 全部比赛
    private static final int GAME_TYPE_RANKING = 1; // 排位赛
    private static final int GAME_TYPE_FRIENDLY = 2; // 友谊赛
    private static final int GAME_TYPE_LEAGUE = 3; // 联赛

    /**
     * 主入口：执行完整的生涯数据初始化
     */
    @Transactional(rollbackFor = Exception.class)
    public InitializationResult initializeCareerData() {
        log.info("🚀 开始执行球员生涯数据初始化 (V2 - 新表结构)...");

        InitializationResult result = new InitializationResult();
        long startTime = System.currentTimeMillis();

        try {
            // 步骤1：初始化赛季管理
            log.info("📅 步骤1：初始化赛季管理");
            SeasonDO currentSeason = seasonService.getCurrentSeason();
            log.info("当前赛季: {} (ID: {})", currentSeason.getSeasonName(), currentSeason.getId());

            // 步骤2：数据质量检查
            log.info("📊 步骤2：执行数据质量检查");
            result.dataQualityReport = performDataQualityCheck();

            // 步骤3：修复比赛赛季关联
            log.info("📅 步骤3：修复比赛赛季关联");
            int gamesLinked = linkGamesToSeasons();
            log.info("完成比赛赛季关联，处理了 {} 场比赛", gamesLinked);

            // 步骤4：生成生涯统计数据
            log.info("🏆 步骤4：生成生涯统计数据");
            result.careerStatsGenerated = generateCareerStats();

            // 步骤5：生成生涯最佳数据记录
            log.info("⭐ 步骤5：生成生涯最佳数据记录");
            // result.bestStatsGenerated = generateBestStats();

            // 步骤6：最终数据验证
            log.info("✅ 步骤6：最终数据验证");
            result.finalValidation = performFinalValidation();

            result.success = true;
            result.executionTimeMs = System.currentTimeMillis() - startTime;

            log.info("🎉 球员生涯数据初始化成功完成！耗时: {}ms", result.executionTimeMs);

        } catch (Exception e) {
            result.success = false;
            result.errorMessage = e.getMessage();
            result.executionTimeMs = System.currentTimeMillis() - startTime;

            log.error("❌ 球员生涯数据初始化失败: {}", e.getMessage(), e);
            throw e;
        }

        return result;
    }

    /**
     * 数据质量检查
     */
    private DataQualityReport performDataQualityCheck() {
        DataQualityReport report = new DataQualityReport();
        
        // 统计球员数量
        List<PlayerDO> allPlayers = playerMapper.selectList(new LambdaQueryWrapper<>());
        report.totalPlayers = allPlayers.size();
        
        // 统计有效比赛数量（已结束且有统计数据的比赛）
        List<GameDO> finishedGames = gameMapper.selectList(
                new LambdaQueryWrapper<GameDO>()
                        .eq(GameDO::getStatus, 4) // 已结束
                        .eq(GameDO::getDeleted, false)
        );
        report.totalFinishedGames = finishedGames.size();
        
        // 统计有统计数据的比赛
        List<PlayerStatisticsDO> allStats = playerStatisticsMapper.selectList(
                new LambdaQueryWrapper<PlayerStatisticsDO>()
                        .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME)
        );
        
        Set<Long> gamesWithStats = allStats.stream()
                .map(PlayerStatisticsDO::getGameId)
                .collect(Collectors.toSet());
        report.gamesWithStats = gamesWithStats.size();
        
        // 统计没有数据的比赛
        Set<Long> finishedGameIds = finishedGames.stream()
                .map(GameDO::getId)
                .collect(Collectors.toSet());
        
        Set<Long> gamesWithoutStats = finishedGameIds.stream()
                .filter(gameId -> !gamesWithStats.contains(gameId))
                .collect(Collectors.toSet());
        report.gamesWithoutStats = gamesWithoutStats.size();
        
        report.totalStatistics = allStats.size();
        
        log.info("📊 数据质量检查完成: 总球员数 {}, 已结束比赛 {}, 有数据比赛 {}, 无数据比赛 {}, 总统计记录 {}", 
                report.totalPlayers, report.totalFinishedGames, report.gamesWithStats, 
                report.gamesWithoutStats, report.totalStatistics);
        
        return report;
    }

    /**
     * 生成赛季统计数据
     */
    private int generateSeasonStats(SeasonDO currentSeason) {
        log.info("开始生成赛季统计数据...");

        try {
            // 1. 清理旧的赛季统计数据
            playerSeasonStatsMapper.delete(new LambdaQueryWrapper<PlayerSeasonStatsDO>()
                    .eq(PlayerSeasonStatsDO::getSeasonId, currentSeason.getId()));

            // 2. 查询所有活跃球员
            List<PlayerDO> activePlayers = playerMapper.selectList(new LambdaQueryWrapper<>());
            int generatedCount = 0;

            for (PlayerDO player : activePlayers) {
                // 为每个球员生成各比赛类型的统计
                for (int gameType = GAME_TYPE_ALL; gameType <= GAME_TYPE_LEAGUE; gameType++) {
                    PlayerSeasonStatsDO seasonStats = generatePlayerSeasonStats(player.getId(), currentSeason.getId(), gameType);
                    if (seasonStats != null && seasonStats.getGamesPlayed() > 0) {
                        playerSeasonStatsMapper.insert(seasonStats);
                        generatedCount++;
                        log.debug("为球员 {} 生成了 {} 类型的赛季统计", player.getId(), gameType);
                    }
                }
            }

            log.info("生成了{}条赛季统计数据", generatedCount);
            return generatedCount;

        } catch (Exception e) {
            log.error("生成赛季统计数据失败", e);
            throw new RuntimeException("赛季统计数据生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成单个球员的赛季统计数据
     */
    private PlayerSeasonStatsDO generatePlayerSeasonStats(Long playerId, Long seasonId, Integer gameType) {
        // 构建查询条件：球员ID + 全场统计 + 赛季ID + 比赛类型
        LambdaQueryWrapper<PlayerStatisticsDO> wrapper = new LambdaQueryWrapper<PlayerStatisticsDO>()
                .eq(PlayerStatisticsDO::getPlayerId, playerId)
                .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME);
        
        // 根据比赛类型和赛季构建SQL条件，只查询有数据的已结束比赛
        if (gameType == GAME_TYPE_ALL) {
            // 全部比赛类型：只需要过滤赛季和状态
            wrapper.inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE season_id = " + seasonId + " AND status = 4 AND deleted = 0");
        } else {
            // 特定比赛类型：需要同时过滤赛季、类型和状态
            wrapper.inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE season_id = " + seasonId + 
                    " AND type = " + gameType + " AND status = 4 AND deleted = 0");
        }
        
        List<PlayerStatisticsDO> playerStats = playerStatisticsMapper.selectList(wrapper);
        
        if (playerStats.isEmpty()) {
            return null;
        }

        // 聚合统计数据
        PlayerSeasonStatsDO seasonStats = new PlayerSeasonStatsDO();
        seasonStats.setPlayerId(playerId);
        seasonStats.setSeasonId(seasonId);
        seasonStats.setGameType(gameType);
        
        // 基础统计
        seasonStats.setGamesPlayed(playerStats.size());
        
        // 总计数据
        seasonStats.setTotalPoints(playerStats.stream().mapToInt(s -> s.getPoints() != null ? s.getPoints() : 0).sum());
        seasonStats.setTotalRebounds(playerStats.stream().mapToInt(s -> 
                (s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0) + 
                (s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0)).sum());
        seasonStats.setTotalOffensiveRebounds(playerStats.stream().mapToInt(s -> s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0).sum());
        seasonStats.setTotalDefensiveRebounds(playerStats.stream().mapToInt(s -> s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0).sum());
        seasonStats.setTotalAssists(playerStats.stream().mapToInt(s -> s.getAssists() != null ? s.getAssists() : 0).sum());
        seasonStats.setTotalSteals(playerStats.stream().mapToInt(s -> s.getSteals() != null ? s.getSteals() : 0).sum());
        seasonStats.setTotalBlocks(playerStats.stream().mapToInt(s -> s.getBlocks() != null ? s.getBlocks() : 0).sum());
        seasonStats.setTotalTurnovers(playerStats.stream().mapToInt(s -> s.getTurnovers() != null ? s.getTurnovers() : 0).sum());
        seasonStats.setTotalFouls(playerStats.stream().mapToInt(s -> s.getFouls() != null ? s.getFouls() : 0).sum());
        
        // 投篮数据
        seasonStats.setTotalFieldGoalsMade(playerStats.stream().mapToInt(s -> 
                (s.getTwoPointMakes() != null ? s.getTwoPointMakes() : 0) + 
                (s.getThreePointMakes() != null ? s.getThreePointMakes() : 0)).sum());
        seasonStats.setTotalFieldGoalsAttempted(playerStats.stream().mapToInt(s -> 
                (s.getTwoPointAttempts() != null ? s.getTwoPointAttempts() : 0) + 
                (s.getThreePointAttempts() != null ? s.getThreePointAttempts() : 0)).sum());
        seasonStats.setTotalTwoPointsMade(playerStats.stream().mapToInt(s -> s.getTwoPointMakes() != null ? s.getTwoPointMakes() : 0).sum());
        seasonStats.setTotalTwoPointsAttempted(playerStats.stream().mapToInt(s -> s.getTwoPointAttempts() != null ? s.getTwoPointAttempts() : 0).sum());
        seasonStats.setTotalThreePointsMade(playerStats.stream().mapToInt(s -> s.getThreePointMakes() != null ? s.getThreePointMakes() : 0).sum());
        seasonStats.setTotalThreePointsAttempted(playerStats.stream().mapToInt(s -> s.getThreePointAttempts() != null ? s.getThreePointAttempts() : 0).sum());
        seasonStats.setTotalFreeThrowsMade(playerStats.stream().mapToInt(s -> s.getFreeThrowMakes() != null ? s.getFreeThrowMakes() : 0).sum());
        seasonStats.setTotalFreeThrowsAttempted(playerStats.stream().mapToInt(s -> s.getFreeThrowAttempts() != null ? s.getFreeThrowAttempts() : 0).sum());

        // 计算平均数据
        int games = seasonStats.getGamesPlayed();
        seasonStats.setAvgPoints(safeDivide(seasonStats.getTotalPoints(), games));
        seasonStats.setAvgRebounds(safeDivide(seasonStats.getTotalRebounds(), games));
        seasonStats.setAvgOffensiveRebounds(safeDivide(seasonStats.getTotalOffensiveRebounds(), games));
        seasonStats.setAvgDefensiveRebounds(safeDivide(seasonStats.getTotalDefensiveRebounds(), games));
        seasonStats.setAvgAssists(safeDivide(seasonStats.getTotalAssists(), games));
        seasonStats.setAvgSteals(safeDivide(seasonStats.getTotalSteals(), games));
        seasonStats.setAvgBlocks(safeDivide(seasonStats.getTotalBlocks(), games));
        seasonStats.setAvgTurnovers(safeDivide(seasonStats.getTotalTurnovers(), games));
        seasonStats.setAvgFouls(safeDivide(seasonStats.getTotalFouls(), games));
        
        // 计算命中率 - 存储为小数格式（0-1），前端显示时再乘以100
        if (seasonStats.getTotalFieldGoalsAttempted() > 0) {
            seasonStats.setFieldGoalPercentage(BigDecimal.valueOf(seasonStats.getTotalFieldGoalsMade())
                    .divide(BigDecimal.valueOf(seasonStats.getTotalFieldGoalsAttempted()), 4, RoundingMode.HALF_UP));
        }

        if (seasonStats.getTotalThreePointsAttempted() > 0) {
            seasonStats.setThreePointPercentage(BigDecimal.valueOf(seasonStats.getTotalThreePointsMade())
                    .divide(BigDecimal.valueOf(seasonStats.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP));
        }

        if (seasonStats.getTotalFreeThrowsAttempted() > 0) {
            seasonStats.setFreeThrowPercentage(BigDecimal.valueOf(seasonStats.getTotalFreeThrowsMade())
                    .divide(BigDecimal.valueOf(seasonStats.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP));
        }

        return seasonStats;
    }

    /**
     * 生成生涯统计数据 - 优化批量处理
     */
    /**
     * 生成生涯统计数据 - 重构版本，逻辑更清晰，性能更好
     */
    private int generateCareerStats() {
        log.info("开始生成生涯统计数据...");
        
        try {
            // 清理旧数据
            cleanupCareerStatsData();
            
            // 第一步：批量计算所有球员的胜负数据
            log.info("📊 第一步：批量计算胜负数据...");
            Map<PlayerGameTypeKey, WinLossData> winLossDataMap = calculateAllPlayersWinLoss();
            log.info("胜负数据计算完成，覆盖 {} 个球员-比赛类型组合", winLossDataMap.size());
            
            // 第二步：批量计算所有球员的统计数据
            log.info("📈 第二步：批量计算统计数据...");
            Map<PlayerGameTypeKey, CareerStatsData> statsDataMap = calculateAllPlayersStats();
            log.info("统计数据计算完成，覆盖 {} 个球员-比赛类型组合", statsDataMap.size());
            
            // 第三步：合并数据并生成最终结果
            log.info("🔗 第三步：合并数据...");
            List<PlayerCareerStatsDO> careerStatsList = mergeWinLossAndStatsData(winLossDataMap, statsDataMap);
            
            // 批量插入生涯统计数据
            if (!careerStatsList.isEmpty()) {
                batchInsertCareerStats(careerStatsList);
            }
            
            log.info("生涯统计数据生成完成，共生成 {} 条记录", careerStatsList.size());
            return careerStatsList.size();
            
        } catch (Exception e) {
            log.error("生成生涯统计数据失败", e);
            throw new RuntimeException("生涯统计数据生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成单个球员的生涯统计数据
     */
    private PlayerCareerStatsDO generatePlayerCareerStats(Long playerId, Integer gameType) {
        // 根据比赛类型查询球员的历史比赛统计数据，只查询有数据的已结束比赛
        LambdaQueryWrapper<PlayerStatisticsDO> wrapper = new LambdaQueryWrapper<PlayerStatisticsDO>()
                .eq(PlayerStatisticsDO::getPlayerId, playerId)
                .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME);
        
        // 如果不是全部比赛类型，需要通过game表关联过滤比赛类型
        if (gameType != GAME_TYPE_ALL) {
            wrapper.inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE type = " + gameType + " AND status = 4 AND deleted = 0");
        } else {
            // 全部比赛类型，只过滤已结束的比赛
            wrapper.inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE status = 4 AND deleted = 0");
        }
        
        List<PlayerStatisticsDO> playerStats = playerStatisticsMapper.selectList(wrapper);
        
        if (playerStats.isEmpty()) {
            return null;
        }

        // 生涯统计数据
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(playerId);
        careerStats.setGameType(gameType);
        
        // 生涯特有字段
        careerStats.setGamesPlayed(playerStats.size());
        careerStats.setValidStatsGames(playerStats.size());
        
        // 计算首场和最近比赛日期
        List<GameDO> playerGames = gameMapper.selectBatchIds(
                playerStats.stream().map(PlayerStatisticsDO::getGameId).collect(Collectors.toList())
        );
        
        if (!playerGames.isEmpty()) {
            LocalDate firstGameDate = playerGames.stream()
                    .filter(game -> game.getStartTime() != null)
                    .map(game -> game.getStartTime().toLocalDate())
                    .min(LocalDate::compareTo)
                    .orElse(null);
            LocalDate latestGameDate = playerGames.stream()
                    .filter(game -> game.getStartTime() != null)
                    .map(game -> game.getStartTime().toLocalDate())
                    .max(LocalDate::compareTo)
                    .orElse(null);
            
            careerStats.setFirstGameDate(firstGameDate);
            careerStats.setLatestGameDate(latestGameDate);
            
            // 计算参赛赛季数
            Set<Long> seasons = playerGames.stream()
                    .filter(game -> game.getSeasonId() != null)
                    .map(GameDO::getSeasonId)
                    .collect(Collectors.toSet());
            careerStats.setTotalSeasons(seasons.size());
        }
        
        // 总计数据
        careerStats.setTotalPoints(playerStats.stream().mapToInt(s -> s.getPoints() != null ? s.getPoints() : 0).sum());
        careerStats.setTotalRebounds(playerStats.stream().mapToInt(s -> 
                (s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0) + 
                (s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0)).sum());
        careerStats.setTotalOffensiveRebounds(playerStats.stream().mapToInt(s -> s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0).sum());
        careerStats.setTotalDefensiveRebounds(playerStats.stream().mapToInt(s -> s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0).sum());
        careerStats.setTotalAssists(playerStats.stream().mapToInt(s -> s.getAssists() != null ? s.getAssists() : 0).sum());
        careerStats.setTotalSteals(playerStats.stream().mapToInt(s -> s.getSteals() != null ? s.getSteals() : 0).sum());
        careerStats.setTotalBlocks(playerStats.stream().mapToInt(s -> s.getBlocks() != null ? s.getBlocks() : 0).sum());
        careerStats.setTotalTurnovers(playerStats.stream().mapToInt(s -> s.getTurnovers() != null ? s.getTurnovers() : 0).sum());
        careerStats.setTotalFouls(playerStats.stream().mapToInt(s -> s.getFouls() != null ? s.getFouls() : 0).sum());
        
        // 投篮数据
        careerStats.setTotalFieldGoalsMade(playerStats.stream().mapToInt(s -> 
                (s.getTwoPointMakes() != null ? s.getTwoPointMakes() : 0) + 
                (s.getThreePointMakes() != null ? s.getThreePointMakes() : 0)).sum());
        careerStats.setTotalFieldGoalsAttempted(playerStats.stream().mapToInt(s -> 
                (s.getTwoPointAttempts() != null ? s.getTwoPointAttempts() : 0) + 
                (s.getThreePointAttempts() != null ? s.getThreePointAttempts() : 0)).sum());
        careerStats.setTotalTwoPointsMade(playerStats.stream().mapToInt(s -> s.getTwoPointMakes() != null ? s.getTwoPointMakes() : 0).sum());
        careerStats.setTotalTwoPointsAttempted(playerStats.stream().mapToInt(s -> s.getTwoPointAttempts() != null ? s.getTwoPointAttempts() : 0).sum());
        careerStats.setTotalThreePointsMade(playerStats.stream().mapToInt(s -> s.getThreePointMakes() != null ? s.getThreePointMakes() : 0).sum());
        careerStats.setTotalThreePointsAttempted(playerStats.stream().mapToInt(s -> s.getThreePointAttempts() != null ? s.getThreePointAttempts() : 0).sum());
        careerStats.setTotalFreeThrowsMade(playerStats.stream().mapToInt(s -> s.getFreeThrowMakes() != null ? s.getFreeThrowMakes() : 0).sum());
        careerStats.setTotalFreeThrowsAttempted(playerStats.stream().mapToInt(s -> s.getFreeThrowAttempts() != null ? s.getFreeThrowAttempts() : 0).sum());
        
        // 计算平均数据
        int games = careerStats.getGamesPlayed();
        careerStats.setAvgPoints(safeDivide(careerStats.getTotalPoints(), games));
        careerStats.setAvgRebounds(safeDivide(careerStats.getTotalRebounds(), games));
        careerStats.setAvgOffensiveRebounds(safeDivide(careerStats.getTotalOffensiveRebounds(), games));
        careerStats.setAvgDefensiveRebounds(safeDivide(careerStats.getTotalDefensiveRebounds(), games));
        careerStats.setAvgAssists(safeDivide(careerStats.getTotalAssists(), games));
        careerStats.setAvgSteals(safeDivide(careerStats.getTotalSteals(), games));
        careerStats.setAvgBlocks(safeDivide(careerStats.getTotalBlocks(), games));
        careerStats.setAvgTurnovers(safeDivide(careerStats.getTotalTurnovers(), games));
        careerStats.setAvgFouls(safeDivide(careerStats.getTotalFouls(), games));
        
        // 计算命中率 - 存储为小数格式（0-1），前端显示时再乘以100
        if (careerStats.getTotalFieldGoalsAttempted() > 0) {
            careerStats.setFieldGoalPercentage(BigDecimal.valueOf(careerStats.getTotalFieldGoalsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalFieldGoalsAttempted()), 4, RoundingMode.HALF_UP));
        }

        if (careerStats.getTotalThreePointsAttempted() > 0) {
            careerStats.setThreePointPercentage(BigDecimal.valueOf(careerStats.getTotalThreePointsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP));
        }

        if (careerStats.getTotalFreeThrowsAttempted() > 0) {
            careerStats.setFreeThrowPercentage(BigDecimal.valueOf(careerStats.getTotalFreeThrowsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP));
        }
        
        return careerStats;
    }

    /**
     * 生成最佳数据记录
     */
    private int generateBestStats() {
        log.info("🏆 开始生成最佳数据记录...");

        try {
            // 清理旧数据
            playerCareerBestStatsMapper.deleteAll();
            
            // 获取所有参赛球员
            List<Long> playerIds = getPlayersWithGameData();
            log.info("发现 {} 个参赛球员需要生成最佳数据", playerIds.size());
            
            int generatedCount = 0;

            for (Long playerId : playerIds) {
                // 为每种比赛类型生成最佳数据
                for (int gameType = GAME_TYPE_ALL; gameType <= GAME_TYPE_LEAGUE; gameType++) {
                    List<PlayerStatisticsDO> playerStats = getPlayerStatsForGameType(playerId, gameType);
                    
                    if (!playerStats.isEmpty()) {
                        PlayerCareerBestStatsDO bestStats = generatePlayerBestStats(playerId, gameType, playerStats);
                        playerCareerBestStatsMapper.insert(bestStats);
                        generatedCount++;
                        log.debug("为球员{} 生成{} 类型的最佳数据", playerId, gameType);
                    }
                }
            }

            log.info("✅ 最佳数据生成完成，共生成 {} 条记录", generatedCount);
            return generatedCount;

        } catch (Exception e) {
            log.error("❌ 生成最佳数据记录失败", e);
            throw new RuntimeException("最佳数据记录生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取球员特定比赛类型的统计数据
     */
    private List<PlayerStatisticsDO> getPlayerStatsForGameType(Long playerId, Integer gameType) {
        LambdaQueryWrapper<PlayerStatisticsDO> wrapper = new LambdaQueryWrapper<PlayerStatisticsDO>()
                .eq(PlayerStatisticsDO::getPlayerId, playerId)
                .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME);
        
        if (gameType == GAME_TYPE_ALL) {
            wrapper.inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE status = 4 AND deleted = 0");
        } else {
            wrapper.inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE type = " + gameType + " AND status = 4 AND deleted = 0");
        }
        
        return playerStatisticsMapper.selectList(wrapper);
    }

    /**
     * 生成球员最佳数据记录
     */
    private PlayerCareerBestStatsDO generatePlayerBestStats(Long playerId, Integer gameType, List<PlayerStatisticsDO> playerStats) {
        PlayerCareerBestStatsDO bestStats = new PlayerCareerBestStatsDO();
        bestStats.setPlayerId(playerId);
        bestStats.setGameType(gameType);

        // 找出最佳得分
        PlayerStatisticsDO bestPointsGame = playerStats.stream()
                .max(Comparator.comparing(s -> s.getPoints() != null ? s.getPoints() : 0))
                .orElse(null);
        
        if (bestPointsGame != null) {
            bestStats.setBestPoints(bestPointsGame.getPoints());
            bestStats.setBestPointsGameId(bestPointsGame.getGameId());
            // 设置最佳得分比赛日期
            GameDO pointsGame = gameMapper.selectById(bestPointsGame.getGameId());
            if (pointsGame != null && pointsGame.getStartTime() != null) {
                bestStats.setBestPointsDate(pointsGame.getStartTime().toLocalDate());
            }
        }

        // 找出最佳篮板
        PlayerStatisticsDO bestReboundsGame = playerStats.stream()
                .max(Comparator.comparing(s -> 
                        (s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0) + 
                        (s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0)))
                .orElse(null);
        
        if (bestReboundsGame != null) {
            int totalRebounds = (bestReboundsGame.getOffensiveRebounds() != null ? bestReboundsGame.getOffensiveRebounds() : 0) +
                              (bestReboundsGame.getDefensiveRebounds() != null ? bestReboundsGame.getDefensiveRebounds() : 0);
            bestStats.setBestRebounds(totalRebounds);
            bestStats.setBestReboundsGameId(bestReboundsGame.getGameId());
            // 设置最佳篮板比赛日期
            GameDO reboundsGame = gameMapper.selectById(bestReboundsGame.getGameId());
            if (reboundsGame != null && reboundsGame.getStartTime() != null) {
                bestStats.setBestReboundsDate(reboundsGame.getStartTime().toLocalDate());
            }
        }

        // 找出最佳助攻
        PlayerStatisticsDO bestAssistsGame = playerStats.stream()
                .max(Comparator.comparing(s -> s.getAssists() != null ? s.getAssists() : 0))
                .orElse(null);
        
        if (bestAssistsGame != null) {
            bestStats.setBestAssists(bestAssistsGame.getAssists());
            bestStats.setBestAssistsGameId(bestAssistsGame.getGameId());
        }

        // 找出最佳抢断
        PlayerStatisticsDO bestStealsGame = playerStats.stream()
                .max(Comparator.comparing(s -> s.getSteals() != null ? s.getSteals() : 0))
                .orElse(null);
        
        if (bestStealsGame != null) {
            bestStats.setBestSteals(bestStealsGame.getSteals());
            bestStats.setBestStealsGameId(bestStealsGame.getGameId());
        }

        // 找出最佳盖帽
        PlayerStatisticsDO bestBlocksGame = playerStats.stream()
                .max(Comparator.comparing(s -> s.getBlocks() != null ? s.getBlocks() : 0))
                .orElse(null);
        
        if (bestBlocksGame != null) {
            bestStats.setBestBlocks(bestBlocksGame.getBlocks());
            bestStats.setBestBlocksGameId(bestBlocksGame.getGameId());
        }

        // 找出最佳三分命中数
        PlayerStatisticsDO bestThreePointsGame = playerStats.stream()
                .max(Comparator.comparing(s -> s.getThreePointMakes() != null ? s.getThreePointMakes() : 0))
                .orElse(null);
        
        if (bestThreePointsGame != null) {
            bestStats.setBestThreePointsMade(bestThreePointsGame.getThreePointMakes());
            bestStats.setBestThreePointsMadeGameId(bestThreePointsGame.getGameId());
        }

        // 找出最佳效率值
        PlayerStatisticsDO bestEfficiencyGame = playerStats.stream()
                .max(Comparator.comparing(s -> s.getEfficiency() != null ? s.getEfficiency() : 0))
                .orElse(null);
        
        if (bestEfficiencyGame != null) {
            bestStats.setBestEfficiency(new BigDecimal(bestEfficiencyGame.getEfficiency() != null ? bestEfficiencyGame.getEfficiency() : 0));
            bestStats.setBestEfficiencyGameId(bestEfficiencyGame.getGameId());
            // 设置最佳效率值比赛日期
            GameDO efficiencyGame = gameMapper.selectById(bestEfficiencyGame.getGameId());
            if (efficiencyGame != null && efficiencyGame.getStartTime() != null) {
                bestStats.setBestEfficiencyDate(efficiencyGame.getStartTime().toLocalDate());
            }
        }

        return bestStats;
    }

    /**
     * 修复比赛赛季关联
     */
    private int linkGamesToSeasons() {
        log.info("🔗 开始修复比赛赛季关联...");
        
        try {
            // 调用赛季服务的初始化方法
            return seasonService.initGameSeasons();
            
        } catch (Exception e) {
            log.error("❌ 比赛赛季关联失败", e);
            throw new RuntimeException("比赛赛季关联失败: " + e.getMessage());
        }
    }

    /**
     * 最终数据验证 - 仅验证生涯数据
     */
    private ValidationResult performFinalValidation() {
        ValidationResult validation = new ValidationResult();

        try {
            // 统计所有球员数量
            validation.totalPlayers = Math.toIntExact(playerMapper.selectCount(new LambdaQueryWrapper<>()));
            
            // 统计参赛球员数量
            List<Long> playersWithData = getPlayersWithGameData();
            validation.playersWithData = playersWithData.size();
            
            // 生涯数据初始化不涉及赛季数据，设为0
            validation.playersWithSeasonData = 0;
            validation.seasonStatsCount = 0;

            // 统计生涯数据数量
            validation.careerStatsCount = Math.toIntExact(playerCareerStatsMapper.selectCount(new LambdaQueryWrapper<>()));
            validation.bestStatsCount = Math.toIntExact(playerCareerBestStatsMapper.selectCount(new LambdaQueryWrapper<>()));

            log.info("生涯数据验证结果: 总球员{}, 参赛球员{}, 生涯统计{}, 生涯最佳记录{}",
                    validation.totalPlayers, validation.playersWithData, 
                    validation.careerStatsCount, validation.bestStatsCount);

        } catch (Exception e) {
            log.error("生涯数据验证失败", e);
            validation.totalPlayers = 0;
            validation.playersWithData = 0;
            validation.playersWithSeasonData = 0;
        }

        return validation;
    }

    /**
     * 获取所有有比赛数据的球员ID列表
     */
    private List<Long> getPlayersWithGameData() {
        // 查询所有有比赛统计数据的球员，只查询有数据的已结束比赛
        return playerStatisticsMapper.selectList(
                new LambdaQueryWrapper<PlayerStatisticsDO>()
                        .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME)
                        .inSql(PlayerStatisticsDO::getGameId, 
                                "SELECT id FROM sd_game WHERE status = 4 AND deleted = 0")
                        .select(PlayerStatisticsDO::getPlayerId)
                        .groupBy(PlayerStatisticsDO::getPlayerId)
        ).stream()
        .map(PlayerStatisticsDO::getPlayerId)
        .distinct()
        .collect(Collectors.toList());
    }

    /**
     * BigDecimal安全除法
     */
    private BigDecimal safeDivide(int numerator, int denominator) {
        if (denominator == 0) return BigDecimal.ZERO;
        return BigDecimal.valueOf(numerator)
                .divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
    }

    /**
     * BigDecimal安全除法 - 重载方法
     */
    private BigDecimal safeDivide(BigDecimal numerator, int denominator) {
        if (denominator <= 0 || numerator == null) {
            return BigDecimal.ZERO;
        }
        return numerator.divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
    }

    // ============= 数据结构定义 =============

    /**
     * 初始化结果
     */
    public static class InitializationResult {
        public boolean success;
        public String errorMessage;
        public long executionTimeMs;
        public DataQualityReport dataQualityReport;
        public int seasonStatsGenerated;
        public int careerStatsGenerated;
        public int bestStatsGenerated;
        public ValidationResult finalValidation;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("======= 生涯数据初始化结果 (V2) =======\n");
            sb.append("执行状态: ").append(success ? "✅ 成功" : "❌ 失败").append("\n");
            sb.append("执行耗时: ").append(executionTimeMs).append("ms\n");
            if (!success) {
                sb.append("错误信息: ").append(errorMessage).append("\n");
            }
            
            if (dataQualityReport != null) {
                sb.append("数据质量: 总球员").append(dataQualityReport.totalPlayers).append("个, ");
                sb.append("已结束比赛").append(dataQualityReport.totalFinishedGames).append("场, ");
                sb.append("有数据比赛").append(dataQualityReport.gamesWithStats).append("场, ");
                sb.append("无数据比赛").append(dataQualityReport.gamesWithoutStats).append("场\n");
            }
            
            sb.append("生涯统计: ").append(careerStatsGenerated).append("条\n");
            sb.append("生涯最佳记录: ").append(bestStatsGenerated).append("条\n");
            sb.append("=====================================");
            return sb.toString();
        }
    }

    /**
     * 数据质量报告
     */
    public static class DataQualityReport {
        public int totalPlayers;
        public int totalFinishedGames;      // 已结束比赛总数
        public int gamesWithStats;          // 有统计数据的比赛数
        public int gamesWithoutStats;       // 没有统计数据的比赛数
        public int totalStatistics;
    }

    /**
     * 验证结果
     */
    public static class ValidationResult {
        public int totalPlayers;           // 总球员数
        public int playersWithData;        // 有生涯数据的球员数（参赛球员）
        public int playersWithSeasonData;  // 本赛季有数据的球员数
        public int seasonStatsCount;       // 赛季统计记录数
        public int careerStatsCount;       // 生涯统计记录数
        public int bestStatsCount;         // 最佳记录数
    }

    /**
     * 清理生涯统计数据（彻底物理删除）
     */
    private void cleanupCareerStatsData() {
        log.info("🧹 彻底清理生涯统计数据...");
        
        // 分步骤彻底清理数据，避免唯一索引冲突
        
                 try {
             // 使用MyBatis-Plus的删除功能清空所有数据
             long careerStatsCount = playerCareerStatsMapper.selectCount(new LambdaQueryWrapper<>());
             long bestStatsCount = playerCareerBestStatsMapper.selectCount(new LambdaQueryWrapper<>());
             
             if (careerStatsCount > 0) {
                 playerCareerStatsMapper.delete(new LambdaQueryWrapper<PlayerCareerStatsDO>()
                     .isNotNull(PlayerCareerStatsDO::getId));
                 log.info("清理了 {} 条生涯统计记录", careerStatsCount);
             }
             
             if (bestStatsCount > 0) {
                 playerCareerBestStatsMapper.delete(new LambdaQueryWrapper<PlayerCareerBestStatsDO>()
                     .isNotNull(PlayerCareerBestStatsDO::getId));
                 log.info("清理了 {} 条最佳数据记录", bestStatsCount);
             }
             
         } catch (Exception e) {
             log.warn("数据清理过程中出现异常，继续执行: {}", e.getMessage());
        }
    }

    // ================== 批量处理优化方法 ==================

    /**
     * 批量获取球员统计数据
     */
    private Map<Long, List<PlayerStatisticsDO>> batchGetPlayerStats(List<Long> playerIds) {
        if (playerIds.isEmpty()) {
            return new HashMap<>();
        }
        
        // 批量查询所有球员的统计数据
        List<PlayerStatisticsDO> allStats = playerStatisticsMapper.selectList(
            new LambdaQueryWrapper<PlayerStatisticsDO>()
                .in(PlayerStatisticsDO::getPlayerId, playerIds)
                .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME)
                .inSql(PlayerStatisticsDO::getGameId, 
                    "SELECT id FROM sd_game WHERE status = 4 AND deleted = 0")
        );
        
        // 按球员ID分组
        return allStats.stream().collect(Collectors.groupingBy(PlayerStatisticsDO::getPlayerId));
    }

    /**
     * 批量获取比赛信息
     */
    private Map<Long, GameDO> batchGetGames(Map<Long, List<PlayerStatisticsDO>> playerStatsMap) {
        Set<Long> gameIds = playerStatsMap.values().stream()
            .flatMap(List::stream)
            .map(PlayerStatisticsDO::getGameId)
            .collect(Collectors.toSet());
        
        if (gameIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<GameDO> games = gameMapper.selectBatchIds(gameIds);
        return games.stream().collect(Collectors.toMap(GameDO::getId, game -> game));
    }

    /**
     * 批量获取球员比赛参与信息
     */
    private Map<Long, List<PlayerGameRelatedDO>> batchGetPlayerGameRelations(List<Long> playerIds) {
        Map<Long, List<PlayerGameRelatedDO>> resultMap = new HashMap<>();
        
        if (playerIds.isEmpty()) {
            return resultMap;
        }
        
        // 批量查询球员比赛关联信息
        List<PlayerGameRelatedDO> allRelations = playerGameRelatedMapper.selectList(
            new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .in(PlayerGameRelatedDO::getPlayerId, playerIds)
                .eq(PlayerGameRelatedDO::getAttend, 2) // 只查询出席的比赛
        );
        
        // 按球员ID分组
        return allRelations.stream().collect(Collectors.groupingBy(PlayerGameRelatedDO::getPlayerId));
    }

    /**
     * 批量插入生涯统计数据
     */
    private void batchInsertCareerStats(List<PlayerCareerStatsDO> careerStatsList) {
        if (careerStatsList.isEmpty()) {
            return;
        }
        
        log.info("开始批量插入{}条生涯统计数据...", careerStatsList.size());
        
        // 使用真正的批量插入，每批500条
        int batchSize = 500;
        int successCount = 0;
        
        for (int i = 0; i < careerStatsList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, careerStatsList.size());
            List<PlayerCareerStatsDO> batch = careerStatsList.subList(i, endIndex);
            
            try {
                // 尝试使用批量插入
                successCount += batch.size();
                
                // 设置创建信息
                batch.forEach(stats -> {
                    stats.setCreator("system");
                    stats.setUpdater("system");
                });
                
                playerCareerStatsMapper.insertBatch(batch);
                log.debug("批量插入第{}批，共{}条记录", (i/batchSize + 1), batch.size());
                
            } catch (Exception e) {
                log.error("批量插入失败: {}", e.getMessage(), e);
                throw new RuntimeException("批量插入生涯统计数据失败", e);
            }
        }
        
        log.info("批量插入完成，成功插入{}条生涯统计数据", successCount);
    }

    /**
     * 优化的生涯统计数据生成方法
     */
    private PlayerCareerStatsDO generatePlayerCareerStatsOptimized(
            Long playerId, Integer gameType, 
            List<PlayerStatisticsDO> allPlayerStats,
            Map<Long, GameDO> gameMap,
            List<PlayerGameRelatedDO> gameRelations) {
        
        // 根据比赛类型过滤统计数据
        List<PlayerStatisticsDO> playerStats = filterStatsByGameType(allPlayerStats, gameType, gameMap);
        
        if (playerStats.isEmpty()) {
            return null;
        }

        // 生涯统计数据
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(playerId);
        careerStats.setGameType(gameType);
        
        // 基础统计
        careerStats.setGamesPlayed(playerStats.size());
        careerStats.setValidStatsGames(playerStats.size());
        
        // 计算生涯日期和赛季
        calculateCareerDatesAndSeasons(careerStats, playerStats, gameMap);
        
        // 计算胜负场数据
        calculateWinLossData(careerStats, playerStats, gameRelations, gameMap);
        
        // 计算基础统计数据
        calculateBasicStats(careerStats, playerStats);
        
        // 计算投篮数据
        calculateShootingStats(careerStats, playerStats);
        
        // 计算平均数据
        calculateAverageStats(careerStats);
        
        // 计算命中率
        calculateShootingPercentages(careerStats);
        
        // 计算高阶数据
        calculateAdvancedStats(careerStats, playerStats);
        
        return careerStats;
    }

    /**
     * 根据比赛类型过滤统计数据
     */
    private List<PlayerStatisticsDO> filterStatsByGameType(
            List<PlayerStatisticsDO> allStats, Integer gameType, Map<Long, GameDO> gameMap) {
        
        if (gameType == GAME_TYPE_ALL) {
            return allStats;
        }
        
        return allStats.stream()
            .filter(stat -> {
                GameDO game = gameMap.get(stat.getGameId());
                return game != null && Objects.equals(game.getType(), gameType);
            })
            .collect(Collectors.toList());
    }

    /**
     * 计算生涯日期和赛季信息
     */
    private void calculateCareerDatesAndSeasons(
            PlayerCareerStatsDO careerStats, 
            List<PlayerStatisticsDO> playerStats,
            Map<Long, GameDO> gameMap) {
        
        List<GameDO> playerGames = playerStats.stream()
            .map(stat -> gameMap.get(stat.getGameId()))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        
        if (!playerGames.isEmpty()) {
            // 首场和最近比赛日期
            LocalDate firstGameDate = playerGames.stream()
                .filter(game -> game.getStartTime() != null)
                .map(game -> game.getStartTime().toLocalDate())
                .min(LocalDate::compareTo)
                .orElse(null);
            LocalDate latestGameDate = playerGames.stream()
                .filter(game -> game.getStartTime() != null)
                .map(game -> game.getStartTime().toLocalDate())
                .max(LocalDate::compareTo)
                .orElse(null);
            
            careerStats.setFirstGameDate(firstGameDate);
            careerStats.setLatestGameDate(latestGameDate);
            
            // 计算参赛赛季数
            Set<Long> seasons = playerGames.stream()
                .filter(game -> game.getSeasonId() != null)
                .map(GameDO::getSeasonId)
                .collect(Collectors.toSet());
            careerStats.setTotalSeasons(seasons.size());
        }
    }

    /**
     * 计算胜负场数据 - 基于实际比赛结果
     */
    private void calculateWinLossData(
            PlayerCareerStatsDO careerStats,
            List<PlayerStatisticsDO> playerStats,
            List<PlayerGameRelatedDO> gameRelations,
            Map<Long, GameDO> gameMap) {
        
        if (gameRelations == null || gameRelations.isEmpty() || playerStats.isEmpty()) {
            careerStats.setTotalWins(0);
            careerStats.setTotalLosses(0);
            careerStats.setWinRate(BigDecimal.ZERO);
            return;
        }
        
        // 获取当前统计对应的比赛ID
        Set<Long> currentGameIds = playerStats.stream()
            .map(PlayerStatisticsDO::getGameId)
            .collect(Collectors.toSet());
        
        int wins = 0;
        int losses = 0;
        int ties = 0; // 平局
        int unknownResults = 0; // 无法判断的比赛
        int gameNotFound = 0; // 找不到的比赛
        int unfinishedGames = 0; // 未结束的比赛
        
        log.debug("球员ID:{} 开始计算胜负，参考比赛数:{}, 关联关系数:{}", 
            careerStats.getPlayerId(), currentGameIds.size(), gameRelations.size());
        
        // 遍历球员参与的每场比赛，判断胜负
        for (PlayerGameRelatedDO relation : gameRelations) {
            if (!currentGameIds.contains(relation.getGameId())) {
                continue; // 跳过非当前统计范围的比赛
            }
            
            GameDO game = gameMap.get(relation.getGameId());
            if (game == null) {
                gameNotFound++;
                continue;
            }
            
            if (game.getStatus() != 4) { // 只统计已结束的比赛
                unfinishedGames++;
                continue;
            }
            
            // 判断球员的胜负情况
            WinLossResult result = determineWinLoss(relation, game);
            switch (result) {
                case WIN:
                    wins++;
                    break;
                case LOSS:
                    losses++;
                    break;
                case TIE:
                    ties++;
                    break;
                case UNKNOWN:
                    unknownResults++;
                    log.debug("无法判断胜负 - 比赛ID:{}, 球员队伍:{}, 主队:{}, 客队:{}, 主队得分:{}, 客队得分:{}", 
                        game.getId(), relation.getTeamId(), game.getHomeTeamId(), game.getGuestTeamId(),
                        game.getHomeTeamPoints(), game.getGuestTeamPoints());
                    break;
            }
        }
        
        log.debug("球员ID:{} 胜负计算完成 - 总比赛:{}, 胜:{}, 负:{}, 平:{}, 无法判断:{}, 未结束:{}, 未找到:{}", 
            careerStats.getPlayerId(), currentGameIds.size(), wins, losses, ties, 
            unknownResults, unfinishedGames, gameNotFound);
        
        careerStats.setTotalWins(wins);
        careerStats.setTotalLosses(losses);
        
        // 计算胜率（平局按0.5胜场计算）- 存储为小数格式（0-1），前端显示时再乘以100
        int totalDecisiveGames = wins + losses; // 有胜负结果的比赛
        if (totalDecisiveGames > 0) {
            double winRate = (wins + ties * 0.5) / (totalDecisiveGames + ties);
            careerStats.setWinRate(BigDecimal.valueOf(winRate).setScale(4, RoundingMode.HALF_UP));
        } else {
            careerStats.setWinRate(BigDecimal.ZERO);
        }

        log.debug("球员ID:{} 胜负统计 - 胜:{} 负:{} 平:{} 胜率:{}",
            careerStats.getPlayerId(), wins, losses, ties, careerStats.getWinRate());
    }
    
    /**
     * 判断球员在某场比赛中的胜负情况
     */
    private WinLossResult determineWinLoss(PlayerGameRelatedDO relation, GameDO game) {
        // 检查比赛数据完整性
        if (game.getHomeTeamPoints() == null || game.getGuestTeamPoints() == null) {
            return WinLossResult.UNKNOWN;
        }
        
        // 获取球员所在队伍
        Long playerTeamId = relation.getTeamId();
        if (playerTeamId == null) {
            return WinLossResult.UNKNOWN;
        }
        
        int homeScore = game.getHomeTeamPoints();
        int guestScore = game.getGuestTeamPoints();
        
        // 判断胜负
        if (homeScore == guestScore) {
            return WinLossResult.TIE; // 平局
        }
        
        boolean playerTeamIsHome = playerTeamId.equals(game.getHomeTeamId());
        boolean playerTeamIsGuest = playerTeamId.equals(game.getGuestTeamId());
        
        if (!playerTeamIsHome && !playerTeamIsGuest) {
            return WinLossResult.UNKNOWN; // 球员队伍与比赛队伍不匹配
        }
        
        if (playerTeamIsHome) {
            return homeScore > guestScore ? WinLossResult.WIN : WinLossResult.LOSS;
        } else {
            return guestScore > homeScore ? WinLossResult.WIN : WinLossResult.LOSS;
        }
    }
    
    /**
     * 胜负结果枚举
     */
    private enum WinLossResult {
        WIN,    // 胜利
        LOSS,   // 失败
        TIE,    // 平局
        UNKNOWN // 无法判断
    }

    /**
     * 球员-比赛类型分组键
     */
    private static class PlayerGameTypeKey {
        private final Long playerId;
        private final Integer gameType;

        public PlayerGameTypeKey(Long playerId, Integer gameType) {
            this.playerId = playerId;
            this.gameType = gameType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            PlayerGameTypeKey that = (PlayerGameTypeKey) o;
            return Objects.equals(playerId, that.playerId) && Objects.equals(gameType, that.gameType);
        }

        @Override
        public int hashCode() {
            return Objects.hash(playerId, gameType);
        }

        public Long getPlayerId() { return playerId; }
        public Integer getGameType() { return gameType; }
    }

    /**
     * 胜负数据
     */
    private static class WinLossData {
        private int wins = 0;
        private int losses = 0;
        private int ties = 0;
        private BigDecimal winRate = BigDecimal.ZERO;

        public void addWin() { wins++; }
        public void addLoss() { losses++; }
        public void addTie() { ties++; }
        
        public void calculateWinRate() {
            int totalDecisiveGames = wins + losses;
            if (totalDecisiveGames > 0) {
                double rate = (wins + ties * 0.5) / (totalDecisiveGames + ties) * 100;
                winRate = BigDecimal.valueOf(rate).setScale(2, RoundingMode.HALF_UP);
            }
        }

        // Getters
        public int getWins() { return wins; }
        public int getLosses() { return losses; }
        public int getTies() { return ties; }
        public BigDecimal getWinRate() { return winRate; }
    }

    /**
     * 统计数据
     */
    @Data
    private static class CareerStatsData {
        private int gamesPlayed = 0;
        private LocalDate firstGameDate;
        private LocalDate latestGameDate;
        private int totalSeasons = 0;
        
        // 基础统计数据
        private int totalPoints = 0;
        private int totalRebounds = 0;
        private int totalOffensiveRebounds = 0;
        private int totalDefensiveRebounds = 0;
        private int totalAssists = 0;
        private int totalSteals = 0;
        private int totalBlocks = 0;
        private int totalTurnovers = 0;
        private int totalFouls = 0;
        
        // 投篮数据
        private int totalFieldGoalsMade = 0;
        private int totalFieldGoalsAttempted = 0;
        private int totalTwoPointsMade = 0;
        private int totalTwoPointsAttempted = 0;
        private int totalThreePointsMade = 0;
        private int totalThreePointsAttempted = 0;
        private int totalFreeThrowsMade = 0;
        private int totalFreeThrowsAttempted = 0;
        private int totalMinutesPlayed = 0;
        
        // 平均数据和高阶统计会在合并时计算
        
        // Getters and Setters
        public int getGamesPlayed() { return gamesPlayed; }
        public void setGamesPlayed(int gamesPlayed) { this.gamesPlayed = gamesPlayed; }
        
        public LocalDate getFirstGameDate() { return firstGameDate; }
        public void setFirstGameDate(LocalDate firstGameDate) { this.firstGameDate = firstGameDate; }
        
        public LocalDate getLatestGameDate() { return latestGameDate; }
        public void setLatestGameDate(LocalDate latestGameDate) { this.latestGameDate = latestGameDate; }
        
        public int getTotalSeasons() { return totalSeasons; }
        public void setTotalSeasons(int totalSeasons) { this.totalSeasons = totalSeasons; }
        
        public int getTotalPoints() { return totalPoints; }
        public void setTotalPoints(int totalPoints) { this.totalPoints = totalPoints; }
        
        public int getTotalRebounds() { return totalRebounds; }
        public void setTotalRebounds(int totalRebounds) { this.totalRebounds = totalRebounds; }
        
        public int getTotalOffensiveRebounds() { return totalOffensiveRebounds; }
        public void setTotalOffensiveRebounds(int totalOffensiveRebounds) { this.totalOffensiveRebounds = totalOffensiveRebounds; }
        
        public int getTotalDefensiveRebounds() { return totalDefensiveRebounds; }
        public void setTotalDefensiveRebounds(int totalDefensiveRebounds) { this.totalDefensiveRebounds = totalDefensiveRebounds; }
        
        public int getTotalAssists() { return totalAssists; }
        public void setTotalAssists(int totalAssists) { this.totalAssists = totalAssists; }
        
        public int getTotalSteals() { return totalSteals; }
        public void setTotalSteals(int totalSteals) { this.totalSteals = totalSteals; }
        
        public int getTotalBlocks() { return totalBlocks; }
        public void setTotalBlocks(int totalBlocks) { this.totalBlocks = totalBlocks; }
        
        public int getTotalTurnovers() { return totalTurnovers; }
        public void setTotalTurnovers(int totalTurnovers) { this.totalTurnovers = totalTurnovers; }
        
        public int getTotalFouls() { return totalFouls; }
        public void setTotalFouls(int totalFouls) { this.totalFouls = totalFouls; }
        
        public int getTotalFieldGoalsMade() { return totalFieldGoalsMade; }
        public void setTotalFieldGoalsMade(int totalFieldGoalsMade) { this.totalFieldGoalsMade = totalFieldGoalsMade; }
        
        public int getTotalFieldGoalsAttempted() { return totalFieldGoalsAttempted; }
        public void setTotalFieldGoalsAttempted(int totalFieldGoalsAttempted) { this.totalFieldGoalsAttempted = totalFieldGoalsAttempted; }
        
        public int getTotalTwoPointsMade() { return totalTwoPointsMade; }
        public void setTotalTwoPointsMade(int totalTwoPointsMade) { this.totalTwoPointsMade = totalTwoPointsMade; }
        
        public int getTotalTwoPointsAttempted() { return totalTwoPointsAttempted; }
        public void setTotalTwoPointsAttempted(int totalTwoPointsAttempted) { this.totalTwoPointsAttempted = totalTwoPointsAttempted; }
        
        public int getTotalThreePointsMade() { return totalThreePointsMade; }
        public void setTotalThreePointsMade(int totalThreePointsMade) { this.totalThreePointsMade = totalThreePointsMade; }
        
        public int getTotalThreePointsAttempted() { return totalThreePointsAttempted; }
        public void setTotalThreePointsAttempted(int totalThreePointsAttempted) { this.totalThreePointsAttempted = totalThreePointsAttempted; }
        
        public int getTotalFreeThrowsMade() { return totalFreeThrowsMade; }
        public void setTotalFreeThrowsMade(int totalFreeThrowsMade) { this.totalFreeThrowsMade = totalFreeThrowsMade; }
        
        public int getTotalFreeThrowsAttempted() { return totalFreeThrowsAttempted; }
        public void setTotalFreeThrowsAttempted(int totalFreeThrowsAttempted) { this.totalFreeThrowsAttempted = totalFreeThrowsAttempted; }
        
    }

    /**
     * 批量计算所有球员的胜负数据
     */
    private Map<PlayerGameTypeKey, WinLossData> calculateAllPlayersWinLoss() {
        log.info("开始批量计算所有球员的胜负数据...");
        
        // 1. 查询所有已结束的比赛
        List<GameDO> finishedGames = gameMapper.selectList(
            new LambdaQueryWrapper<GameDO>()
                .eq(GameDO::getStatus, 4) // 已结束
                .isNotNull(GameDO::getHomeTeamPoints)
                .isNotNull(GameDO::getGuestTeamPoints)
        );
        log.info("找到 {} 场已结束的比赛", finishedGames.size());
        
        if (finishedGames.isEmpty()) {
            return new HashMap<>();
        }
        
        // 2. 建立gameId -> GameDO的映射
        Map<Long, GameDO> gameMap = finishedGames.stream()
            .collect(Collectors.toMap(GameDO::getId, game -> game));
        
        // 3. 查询所有球员比赛关系
        Set<Long> gameIds = gameMap.keySet();
        List<PlayerGameRelatedDO> allRelations = playerGameRelatedMapper.selectList(
            new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .in(PlayerGameRelatedDO::getGameId, gameIds)
        );
        log.info("找到 {} 条球员比赛关系记录", allRelations.size());
        
        // 4. 按球员和比赛类型分组计算胜负
        Map<PlayerGameTypeKey, WinLossData> resultMap = new HashMap<>();
        
        for (PlayerGameRelatedDO relation : allRelations) {
            GameDO game = gameMap.get(relation.getGameId());
            if (game == null) continue;
            
            WinLossResult result = determineWinLoss(relation, game);
            
            // 为每种比赛类型统计
            int[] gameTypes = {GAME_TYPE_ALL, game.getType()};
            
            for (int gameType : gameTypes) {
                if (gameType < GAME_TYPE_ALL || gameType > GAME_TYPE_LEAGUE) continue;
                
                PlayerGameTypeKey key = new PlayerGameTypeKey(relation.getPlayerId(), gameType);
                WinLossData winLossData = resultMap.computeIfAbsent(key, k -> new WinLossData());
                
                switch (result) {
                    case WIN:
                        winLossData.addWin();
                        break;
                    case LOSS:
                        winLossData.addLoss();
                        break;
                    case TIE:
                        winLossData.addTie();
                        break;
                    default:
                        // UNKNOWN不计入胜负统计
                        break;
                }
            }
        }
        
        // 5. 计算胜率
        resultMap.values().forEach(WinLossData::calculateWinRate);
        
        log.info("胜负数据计算完成，覆盖 {} 个球员-比赛类型组合", resultMap.size());
        return resultMap;
    }

    /**
     * 批量计算所有球员的统计数据
     */
    private Map<PlayerGameTypeKey, CareerStatsData> calculateAllPlayersStats() {
        log.info("开始批量计算所有球员的统计数据...");
        
        // 1. 查询所有有效的比赛统计数据
        List<PlayerStatisticsDO> allStats = playerStatisticsMapper.selectList(
            new LambdaQueryWrapper<PlayerStatisticsDO>()
                .eq(PlayerStatisticsDO::getSection, STATS_SECTION_FULL_GAME) // 全场数据
        );
        log.info("找到 {} 条有效的统计记录", allStats.size());
        
        if (allStats.isEmpty()) {
            return new HashMap<>();
        }
        
        // 2. 建立gameId -> GameDO的映射，用于获取比赛类型
        Set<Long> gameIds = allStats.stream()
            .map(PlayerStatisticsDO::getGameId)
            .collect(Collectors.toSet());
        
        List<GameDO> games = gameMapper.selectBatchIds(gameIds);
        Map<Long, GameDO> gameMap = games.stream()
            .collect(Collectors.toMap(GameDO::getId, game -> game));
        log.info("关联了 {} 场比赛信息", games.size());
        
        // 3. 按球员和比赛类型分组
        Map<PlayerGameTypeKey, List<PlayerStatisticsDO>> groupedStats = new HashMap<>();
        
        for (PlayerStatisticsDO stat : allStats) {
            GameDO game = gameMap.get(stat.getGameId());
            if (game == null) continue;
            
            // 为全部比赛类型和具体比赛类型都添加记录
            int[] gameTypes = {GAME_TYPE_ALL, game.getType()};
            
            for (int gameType : gameTypes) {
                if (gameType < GAME_TYPE_ALL || gameType > GAME_TYPE_LEAGUE) continue;
                
                PlayerGameTypeKey key = new PlayerGameTypeKey(stat.getPlayerId(), gameType);
                groupedStats.computeIfAbsent(key, k -> new ArrayList<>()).add(stat);
            }
        }
        
        log.info("统计数据分组完成，覆盖 {} 个球员-比赛类型组合", groupedStats.size());
        
        // 4. 为每个分组计算统计数据
        Map<PlayerGameTypeKey, CareerStatsData> resultMap = new HashMap<>();
        
        for (Map.Entry<PlayerGameTypeKey, List<PlayerStatisticsDO>> entry : groupedStats.entrySet()) {
            PlayerGameTypeKey key = entry.getKey();
            List<PlayerStatisticsDO> playerStats = entry.getValue();
            
            CareerStatsData statsData = calculateStatsForPlayer(playerStats, gameMap);
            resultMap.put(key, statsData);
        }
        
        log.info("统计数据计算完成，覆盖 {} 个球员-比赛类型组合", resultMap.size());
        return resultMap;
    }

    /**
     * 为单个球员计算统计数据
     */
    private CareerStatsData calculateStatsForPlayer(List<PlayerStatisticsDO> playerStats, Map<Long, GameDO> gameMap) {
        CareerStatsData statsData = new CareerStatsData();
        
        // 基础数据
        statsData.setGamesPlayed(playerStats.size());
        
        // 计算首场和最近比赛日期
        List<LocalDate> gameDates = playerStats.stream()
            .map(stat -> gameMap.get(stat.getGameId()))
            .filter(Objects::nonNull)
            .filter(game -> game.getStartTime() != null)
            .map(game -> game.getStartTime().toLocalDate())
            .sorted()
            .collect(Collectors.toList());
        
        if (!gameDates.isEmpty()) {
            statsData.setFirstGameDate(gameDates.get(0));
            statsData.setLatestGameDate(gameDates.get(gameDates.size() - 1));
        }
        
        // 计算参赛赛季数
        Set<Long> seasons = playerStats.stream()
            .map(stat -> gameMap.get(stat.getGameId()))
            .filter(Objects::nonNull)
            .filter(game -> game.getSeasonId() != null)
            .map(GameDO::getSeasonId)
            .collect(Collectors.toSet());
        statsData.setTotalSeasons(seasons.size());
        
        // 计算累计统计数据
        for (PlayerStatisticsDO stat : playerStats) {
            statsData.setTotalPoints(statsData.getTotalPoints() + (stat.getPoints() != null ? stat.getPoints() : 0));
            
            int rebounds = (stat.getOffensiveRebounds() != null ? stat.getOffensiveRebounds() : 0) + 
                          (stat.getDefensiveRebounds() != null ? stat.getDefensiveRebounds() : 0);
            statsData.setTotalRebounds(statsData.getTotalRebounds() + rebounds);
            statsData.setTotalOffensiveRebounds(statsData.getTotalOffensiveRebounds() + 
                (stat.getOffensiveRebounds() != null ? stat.getOffensiveRebounds() : 0));
            statsData.setTotalDefensiveRebounds(statsData.getTotalDefensiveRebounds() + 
                (stat.getDefensiveRebounds() != null ? stat.getDefensiveRebounds() : 0));
            
            statsData.setTotalAssists(statsData.getTotalAssists() + (stat.getAssists() != null ? stat.getAssists() : 0));
            statsData.setTotalSteals(statsData.getTotalSteals() + (stat.getSteals() != null ? stat.getSteals() : 0));
            statsData.setTotalBlocks(statsData.getTotalBlocks() + (stat.getBlocks() != null ? stat.getBlocks() : 0));
            statsData.setTotalTurnovers(statsData.getTotalTurnovers() + (stat.getTurnovers() != null ? stat.getTurnovers() : 0));
            statsData.setTotalFouls(statsData.getTotalFouls() + (stat.getFouls() != null ? stat.getFouls() : 0));
            
            // 投篮数据
            int fieldGoalsMade = (stat.getTwoPointMakes() != null ? stat.getTwoPointMakes() : 0) + 
                                (stat.getThreePointMakes() != null ? stat.getThreePointMakes() : 0);
            int fieldGoalsAttempted = (stat.getTwoPointAttempts() != null ? stat.getTwoPointAttempts() : 0) + 
                                     (stat.getThreePointAttempts() != null ? stat.getThreePointAttempts() : 0);
            
            statsData.setTotalFieldGoalsMade(statsData.getTotalFieldGoalsMade() + fieldGoalsMade);
            statsData.setTotalFieldGoalsAttempted(statsData.getTotalFieldGoalsAttempted() + fieldGoalsAttempted);
            statsData.setTotalTwoPointsMade(statsData.getTotalTwoPointsMade() + 
                (stat.getTwoPointMakes() != null ? stat.getTwoPointMakes() : 0));
            statsData.setTotalTwoPointsAttempted(statsData.getTotalTwoPointsAttempted() + 
                (stat.getTwoPointAttempts() != null ? stat.getTwoPointAttempts() : 0));
            statsData.setTotalThreePointsMade(statsData.getTotalThreePointsMade() + 
                (stat.getThreePointMakes() != null ? stat.getThreePointMakes() : 0));
            statsData.setTotalThreePointsAttempted(statsData.getTotalThreePointsAttempted() + 
                (stat.getThreePointAttempts() != null ? stat.getThreePointAttempts() : 0));
            statsData.setTotalFreeThrowsMade(statsData.getTotalFreeThrowsMade() + 
                (stat.getFreeThrowMakes() != null ? stat.getFreeThrowMakes() : 0));
            statsData.setTotalFreeThrowsAttempted(statsData.getTotalFreeThrowsAttempted() + 
                (stat.getFreeThrowAttempts() != null ? stat.getFreeThrowAttempts() : 0));
            
        
            
            // 上场时间(从秒转换为分钟)
            if (stat.getPlayingTime() != null && stat.getPlayingTime() > 0) {
                BigDecimal minutes = BigDecimal.valueOf(stat.getPlayingTime()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                statsData.setTotalMinutesPlayed(statsData.getTotalMinutesPlayed().add(minutes));
            }
        }
        
        return statsData;
    }

    /**
     * 合并胜负数据和统计数据
     */
    private List<PlayerCareerStatsDO> mergeWinLossAndStatsData(
            Map<PlayerGameTypeKey, WinLossData> winLossDataMap,
            Map<PlayerGameTypeKey, CareerStatsData> statsDataMap) {
        
        log.info("开始合并胜负数据和统计数据...");
        
        // 获取所有的键，包括只有胜负数据或只有统计数据的
        Set<PlayerGameTypeKey> allKeys = new HashSet<>();
        allKeys.addAll(winLossDataMap.keySet());
        allKeys.addAll(statsDataMap.keySet());
        
        List<PlayerCareerStatsDO> resultList = new ArrayList<>();
        
        for (PlayerGameTypeKey key : allKeys) {
            WinLossData winLossData = winLossDataMap.get(key);
            CareerStatsData statsData = statsDataMap.get(key);
            
            // 至少要有统计数据才创建记录
            if (statsData == null || statsData.getGamesPlayed() == 0) {
                continue;
            }
            
            PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
            careerStats.setPlayerId(key.getPlayerId());
            careerStats.setGameType(key.getGameType());
            
            // 设置生涯特有字段
            careerStats.setGamesPlayed(statsData.getGamesPlayed());
            careerStats.setValidStatsGames(statsData.getGamesPlayed());
            careerStats.setTotalSeasons(statsData.getTotalSeasons());
            careerStats.setFirstGameDate(statsData.getFirstGameDate());
            careerStats.setLatestGameDate(statsData.getLatestGameDate());
            
            // 设置胜负数据
            if (winLossData != null) {
                careerStats.setTotalWins(winLossData.getWins());
                careerStats.setTotalLosses(winLossData.getLosses());
                careerStats.setWinRate(winLossData.getWinRate());
                // 计算连胜数据
                calculateStreakData(careerStats, key.getPlayerId(), key.getGameType());
            }
            
            // 设置统计数据
            setBasicStatsFromData(careerStats, statsData);
            
            // 计算平均数据
            calculateAverageStatsFromData(careerStats);
            
            // 计算命中率
            calculateShootingPercentagesFromData(careerStats);
            
            // 设置创建者
            careerStats.setCreator("system");
            
            resultList.add(careerStats);
        }
        
        log.info("数据合并完成，生成 {} 条生涯统计记录", resultList.size());
        return resultList;
    }

    /**
     * 从CareerStatsData设置基础统计数据
     */
    private void setBasicStatsFromData(PlayerCareerStatsDO careerStats, CareerStatsData statsData) {
        careerStats.setTotalPoints(statsData.getTotalPoints());
        careerStats.setTotalRebounds(statsData.getTotalRebounds());
        careerStats.setTotalOffensiveRebounds(statsData.getTotalOffensiveRebounds());
        careerStats.setTotalDefensiveRebounds(statsData.getTotalDefensiveRebounds());
        careerStats.setTotalAssists(statsData.getTotalAssists());
        careerStats.setTotalSteals(statsData.getTotalSteals());
        careerStats.setTotalBlocks(statsData.getTotalBlocks());
        careerStats.setTotalTurnovers(statsData.getTotalTurnovers());
        careerStats.setTotalFouls(statsData.getTotalFouls());
        careerStats.setTotalFieldGoalsMade(statsData.getTotalFieldGoalsMade());
        careerStats.setTotalFieldGoalsAttempted(statsData.getTotalFieldGoalsAttempted());
        careerStats.setTotalTwoPointsMade(statsData.getTotalTwoPointsMade());
        careerStats.setTotalTwoPointsAttempted(statsData.getTotalTwoPointsAttempted());
        careerStats.setTotalThreePointsMade(statsData.getTotalThreePointsMade());
        careerStats.setTotalThreePointsAttempted(statsData.getTotalThreePointsAttempted());
        careerStats.setTotalFreeThrowsMade(statsData.getTotalFreeThrowsMade());
        careerStats.setTotalFreeThrowsAttempted(statsData.getTotalFreeThrowsAttempted());
        careerStats.setTotalMinutesPlayed(statsData.getTotalMinutesPlayed());
    }

    /**
     * 计算平均数据
     */
    private void calculateAverageStatsFromData(PlayerCareerStatsDO careerStats) {
        int games = careerStats.getGamesPlayed();
        if (games > 0) {
            careerStats.setAvgPoints(safeDivide(careerStats.getTotalPoints(), games));
            careerStats.setAvgRebounds(safeDivide(careerStats.getTotalRebounds(), games));
            careerStats.setAvgOffensiveRebounds(safeDivide(careerStats.getTotalOffensiveRebounds(), games));
            careerStats.setAvgDefensiveRebounds(safeDivide(careerStats.getTotalDefensiveRebounds(), games));
            careerStats.setAvgAssists(safeDivide(careerStats.getTotalAssists(), games));
            careerStats.setAvgSteals(safeDivide(careerStats.getTotalSteals(), games));
            careerStats.setAvgBlocks(safeDivide(careerStats.getTotalBlocks(), games));
            careerStats.setAvgTurnovers(safeDivide(careerStats.getTotalTurnovers(), games));
            careerStats.setAvgFouls(safeDivide(careerStats.getTotalFouls(), games));
            careerStats.setAvgMinutesPlayed(safeDivide(careerStats.getTotalMinutesPlayed(), games));
        }
    }

    /**
     * 计算命中率
     */
    private void calculateShootingPercentagesFromData(PlayerCareerStatsDO careerStats) {
        // 投篮命中率 - 存储为小数(0-1)，不乘以100
        if (careerStats.getTotalFieldGoalsAttempted() > 0) {
            careerStats.setFieldGoalPercentage(
                BigDecimal.valueOf(careerStats.getTotalFieldGoalsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalFieldGoalsAttempted()), 4, RoundingMode.HALF_UP)
            );
        }
        
        // 三分命中率 - 存储为小数(0-1)，不乘以100
        if (careerStats.getTotalThreePointsAttempted() > 0) {
            careerStats.setThreePointPercentage(
                BigDecimal.valueOf(careerStats.getTotalThreePointsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP)
            );
        }
        
        // 二分命中率 - 存储为小数(0-1)，不乘以100
        if (careerStats.getTotalTwoPointsAttempted() > 0) {
            careerStats.setTwoPointPercentage(
                BigDecimal.valueOf(careerStats.getTotalTwoPointsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalTwoPointsAttempted()), 4, RoundingMode.HALF_UP)
            );
        }
        
        // 罚球命中率 - 存储为小数(0-1)，不乘以100
        if (careerStats.getTotalFreeThrowsAttempted() > 0) {
            careerStats.setFreeThrowPercentage(
                BigDecimal.valueOf(careerStats.getTotalFreeThrowsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP)
            );
        }
        
        // 真实命中率 - 存储为小数(0-1)，不乘以100
        int totalFGA = careerStats.getTotalFieldGoalsAttempted();
        int totalFTA = careerStats.getTotalFreeThrowsAttempted();
        int totalPoints = careerStats.getTotalPoints();
        
        if (totalFGA + 0.44 * totalFTA > 0) {
            double tsPct = totalPoints / (2.0 * (totalFGA + 0.44 * totalFTA));
            careerStats.setTrueShootingPercentage(
                BigDecimal.valueOf(tsPct).setScale(4, RoundingMode.HALF_UP)
            );
        }
        
        // 有效投篮命中率 - 存储为小数(0-1)，不乘以100
        if (totalFGA > 0) {
            double eFGPct = (careerStats.getTotalFieldGoalsMade() + 0.5 * careerStats.getTotalThreePointsMade()) / totalFGA;
            careerStats.setEffectiveFieldGoalPercentage(
                BigDecimal.valueOf(eFGPct).setScale(4, RoundingMode.HALF_UP)
            );
        }
    }
    
    /**
     * 计算基础统计数据
     */
    private void calculateBasicStats(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> playerStats) {
        careerStats.setTotalPoints(playerStats.stream().mapToInt(s -> s.getPoints() != null ? s.getPoints() : 0).sum());
        careerStats.setTotalRebounds(playerStats.stream().mapToInt(s -> 
            (s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0) + 
            (s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0)).sum());
        careerStats.setTotalOffensiveRebounds(playerStats.stream().mapToInt(s -> s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0).sum());
        careerStats.setTotalDefensiveRebounds(playerStats.stream().mapToInt(s -> s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0).sum());
        careerStats.setTotalAssists(playerStats.stream().mapToInt(s -> s.getAssists() != null ? s.getAssists() : 0).sum());
        careerStats.setTotalSteals(playerStats.stream().mapToInt(s -> s.getSteals() != null ? s.getSteals() : 0).sum());
        careerStats.setTotalBlocks(playerStats.stream().mapToInt(s -> s.getBlocks() != null ? s.getBlocks() : 0).sum());
        careerStats.setTotalTurnovers(playerStats.stream().mapToInt(s -> s.getTurnovers() != null ? s.getTurnovers() : 0).sum());
        careerStats.setTotalFouls(playerStats.stream().mapToInt(s -> s.getFouls() != null ? s.getFouls() : 0).sum());
        
        // 出场时间（转换为分钟）
        int totalMinutesInt = playerStats.stream()
            .filter(s -> s.getPlayingTime() != null)
            .mapToInt(PlayerStatisticsDO::getPlayingTime)
            .sum();
        BigDecimal totalMinutes = BigDecimal.valueOf(totalMinutesInt);
        careerStats.setTotalMinutesPlayed(totalMinutes);
    }

    /**
     * 计算投篮数据
     */
    private void calculateShootingStats(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> playerStats) {
        careerStats.setTotalFieldGoalsMade(playerStats.stream().mapToInt(s -> 
            (s.getTwoPointMakes() != null ? s.getTwoPointMakes() : 0) + 
            (s.getThreePointMakes() != null ? s.getThreePointMakes() : 0)).sum());
        careerStats.setTotalFieldGoalsAttempted(playerStats.stream().mapToInt(s -> 
            (s.getTwoPointAttempts() != null ? s.getTwoPointAttempts() : 0) + 
            (s.getThreePointAttempts() != null ? s.getThreePointAttempts() : 0)).sum());
        careerStats.setTotalThreePointsMade(playerStats.stream().mapToInt(s -> s.getThreePointMakes() != null ? s.getThreePointMakes() : 0).sum());
        careerStats.setTotalThreePointsAttempted(playerStats.stream().mapToInt(s -> s.getThreePointAttempts() != null ? s.getThreePointAttempts() : 0).sum());
        careerStats.setTotalFreeThrowsMade(playerStats.stream().mapToInt(s -> s.getFreeThrowMakes() != null ? s.getFreeThrowMakes() : 0).sum());
        careerStats.setTotalFreeThrowsAttempted(playerStats.stream().mapToInt(s -> s.getFreeThrowAttempts() != null ? s.getFreeThrowAttempts() : 0).sum());
    }

    /**
     * 计算平均数据
     */
    private void calculateAverageStats(PlayerCareerStatsDO careerStats) {
        int games = careerStats.getGamesPlayed();
        careerStats.setAvgPoints(safeDivide(careerStats.getTotalPoints(), games));
        careerStats.setAvgRebounds(safeDivide(careerStats.getTotalRebounds(), games));
        careerStats.setAvgOffensiveRebounds(safeDivide(careerStats.getTotalOffensiveRebounds(), games));
        careerStats.setAvgDefensiveRebounds(safeDivide(careerStats.getTotalDefensiveRebounds(), games));
        careerStats.setAvgAssists(safeDivide(careerStats.getTotalAssists(), games));
        careerStats.setAvgSteals(safeDivide(careerStats.getTotalSteals(), games));
        careerStats.setAvgBlocks(safeDivide(careerStats.getTotalBlocks(), games));
        careerStats.setAvgTurnovers(safeDivide(careerStats.getTotalTurnovers(), games));
        careerStats.setAvgFouls(safeDivide(careerStats.getTotalFouls(), games));
        
        // 场均出场时间
        if (games > 0 && careerStats.getTotalMinutesPlayed() != null) {
            careerStats.setAvgMinutesPlayed(careerStats.getTotalMinutesPlayed()
                .divide(BigDecimal.valueOf(games), 2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 计算命中率 - 存储为小数形式（0-1），前端显示时再乘以100
     */
    private void calculateShootingPercentages(PlayerCareerStatsDO careerStats) {
        // 投篮命中率 - 存储为小数（如0.42表示42%）
        if (careerStats.getTotalFieldGoalsAttempted() > 0) {
            careerStats.setFieldGoalPercentage(BigDecimal.valueOf(careerStats.getTotalFieldGoalsMade())
                .divide(BigDecimal.valueOf(careerStats.getTotalFieldGoalsAttempted()), 4, RoundingMode.HALF_UP));
        }
        
        // 三分命中率 - 存储为小数
        if (careerStats.getTotalThreePointsAttempted() > 0) {
            careerStats.setThreePointPercentage(BigDecimal.valueOf(careerStats.getTotalThreePointsMade())
                .divide(BigDecimal.valueOf(careerStats.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP));
        }
        
        // 二分命中率 - 存储为小数
        int twoPointMade = careerStats.getTotalFieldGoalsMade() - careerStats.getTotalThreePointsMade();
        int twoPointAttempted = careerStats.getTotalFieldGoalsAttempted() - careerStats.getTotalThreePointsAttempted();
        if (twoPointAttempted > 0) {
            careerStats.setTwoPointPercentage(BigDecimal.valueOf(twoPointMade)
                .divide(BigDecimal.valueOf(twoPointAttempted), 4, RoundingMode.HALF_UP));
        }
        
        // 罚球命中率 - 存储为小数
        if (careerStats.getTotalFreeThrowsAttempted() > 0) {
            careerStats.setFreeThrowPercentage(BigDecimal.valueOf(careerStats.getTotalFreeThrowsMade())
                .divide(BigDecimal.valueOf(careerStats.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP));
        }
        
        log.debug("命中率计算 - 投篮:{}, 三分:{}, 罚球:{}", 
            careerStats.getFieldGoalPercentage(), careerStats.getThreePointPercentage(), careerStats.getFreeThrowPercentage());
    }

    /**
     * 计算高阶数据
     */
    private void calculateAdvancedStats(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> playerStats) {
        // 真实命中率 (TS%) = Points / (2 * (FGA + 0.44 * FTA)) - 存储为小数
        int totalFGA = careerStats.getTotalFieldGoalsAttempted();
        int totalFTA = careerStats.getTotalFreeThrowsAttempted();
        if (totalFGA > 0 || totalFTA > 0) {
            double denominator = 2 * (totalFGA + 0.44 * totalFTA);
            if (denominator > 0) {
                careerStats.setTrueShootingPercentage(
                    BigDecimal.valueOf(careerStats.getTotalPoints())
                        .divide(BigDecimal.valueOf(denominator), 4, RoundingMode.HALF_UP)
                );
            }
        }
        
        // 有效投篮命中率 (eFG%) = (FGM + 0.5 * 3PM) / FGA - 存储为小数
        if (totalFGA > 0) {
            double effectiveFGM = careerStats.getTotalFieldGoalsMade() + 0.5 * careerStats.getTotalThreePointsMade();
            careerStats.setEffectiveFieldGoalPercentage(
                BigDecimal.valueOf(effectiveFGM)
                    .divide(BigDecimal.valueOf(totalFGA), 4, RoundingMode.HALF_UP)
            );
        }
        
        // 助攻失误比
        if (careerStats.getTotalTurnovers() > 0) {
            careerStats.setAssistTurnoverRatio(
                BigDecimal.valueOf(careerStats.getTotalAssists())
                    .divide(BigDecimal.valueOf(careerStats.getTotalTurnovers()), 2, RoundingMode.HALF_UP)
            );
        }
        
        // 计算平均效率值
        OptionalDouble avgEfficiency = playerStats.stream()
            .filter(s -> s.getEfficiency() != null)
            .mapToInt(PlayerStatisticsDO::getEfficiency)
            .average();
        if (avgEfficiency.isPresent()) {
            careerStats.setAvgEfficiency(BigDecimal.valueOf(avgEfficiency.getAsDouble())
                .setScale(2, RoundingMode.HALF_UP));
        }
        
        // 计算平均净胜分
        OptionalDouble avgPlusMinus = playerStats.stream()
            .filter(s -> s.getPlusMinus() != null)
            .mapToInt(PlayerStatisticsDO::getPlusMinus)
            .average();
        if (avgPlusMinus.isPresent()) {
            careerStats.setPlusMinus(BigDecimal.valueOf(avgPlusMinus.getAsDouble())
                .setScale(2, RoundingMode.HALF_UP));
        }
    }
    
    /**
     * 计算连胜数据
     */
    private void calculateStreakData(PlayerCareerStatsDO careerStats, Long playerId, Integer gameType) {
        try {
            // 查询球员的所有比赛记录，按时间排序
            List<PlayerGameRelatedDO> gameRelations = getPlayerGameRelationsByType(playerId, gameType);
            
            if (gameRelations.isEmpty()) {
                careerStats.setCurrentStreak(0);
                careerStats.setMaxWinStreak(0);
                careerStats.setMaxLoseStreak(0);
                return;
            }
            
            // 获取比赛信息并按时间排序
            Set<Long> gameIds = gameRelations.stream()
                .map(PlayerGameRelatedDO::getGameId)
                .collect(Collectors.toSet());
            
            List<GameDO> games = gameMapper.selectList(
                new LambdaQueryWrapper<GameDO>()
                    .in(GameDO::getId, gameIds)
                    .eq(GameDO::getStatus, 4) // 只统计已结束的比赛
                    .orderByAsc(GameDO::getStartTime)
            );
            
            if (games.isEmpty()) {
                careerStats.setCurrentStreak(0);
                careerStats.setMaxWinStreak(0);
                careerStats.setMaxLoseStreak(0);
                return;
            }
            
            // 建立比赛ID到关系的映射
            Map<Long, PlayerGameRelatedDO> relationMap = gameRelations.stream()
                .collect(Collectors.toMap(PlayerGameRelatedDO::getGameId, r -> r));
            
            // 计算连胜连败数据
            int currentStreak = 0;
            int maxWinStreak = 0;
            int maxLoseStreak = 0;
            int tempWinStreak = 0;
            int tempLoseStreak = 0;
            WinLossResult lastResult = null;
            
            for (GameDO game : games) {
                PlayerGameRelatedDO relation = relationMap.get(game.getId());
                if (relation == null) continue;
                
                WinLossResult result = determineWinLoss(relation, game);
                
                if (result == WinLossResult.WIN) {
                    tempWinStreak++;
                    tempLoseStreak = 0;
                    maxWinStreak = Math.max(maxWinStreak, tempWinStreak);
                    
                    if (lastResult == WinLossResult.WIN || lastResult == null) {
                        currentStreak = tempWinStreak;
                    } else {
                        currentStreak = 1;
                    }
                } else if (result == WinLossResult.LOSS) {
                    tempLoseStreak++;
                    tempWinStreak = 0;
                    maxLoseStreak = Math.max(maxLoseStreak, tempLoseStreak);
                    
                    if (lastResult == WinLossResult.LOSS || lastResult == null) {
                        currentStreak = -tempLoseStreak;
                    } else {
                        currentStreak = -1;
                    }
                } else {
                    // 平局或无法判断，中断连胜连败
                    tempWinStreak = 0;
                    tempLoseStreak = 0;
                    currentStreak = 0;
                }
                
                lastResult = result;
            }
            
            careerStats.setCurrentStreak(currentStreak);
            careerStats.setMaxWinStreak(maxWinStreak);
            careerStats.setMaxLoseStreak(maxLoseStreak);
            
            // 设置连胜开始日期
            if (currentStreak != 0 && !games.isEmpty()) {
                // 找到当前连胜/连败的开始比赛
                int streakLength = Math.abs(currentStreak);
                if (games.size() >= streakLength) {
                    GameDO streakStartGame = games.get(games.size() - streakLength);
                    if (streakStartGame.getStartTime() != null) {
                        careerStats.setStreakStartDate(streakStartGame.getStartTime().toLocalDate());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("计算连胜数据失败 - 球员ID:{}, 比赛类型:{}", playerId, gameType, e);
            // 出错时设置默认值
            careerStats.setCurrentStreak(0);
            careerStats.setMaxWinStreak(0);
            careerStats.setMaxLoseStreak(0);
        }
    }
    
    /**
     * 根据比赛类型获取球员比赛关系
     */
    private List<PlayerGameRelatedDO> getPlayerGameRelationsByType(Long playerId, Integer gameType) {
        LambdaQueryWrapper<PlayerGameRelatedDO> wrapper = new LambdaQueryWrapper<PlayerGameRelatedDO>()
            .eq(PlayerGameRelatedDO::getPlayerId, playerId);
        
        if (gameType != GAME_TYPE_ALL) {
            // 特定比赛类型，需要通过game表过滤
            wrapper.inSql(PlayerGameRelatedDO::getGameId, 
                "SELECT id FROM sd_game WHERE type = " + gameType + " AND status = 4 AND deleted = 0");
        } else {
            // 全部比赛类型，只过滤已结束的比赛
            wrapper.inSql(PlayerGameRelatedDO::getGameId, 
                "SELECT id FROM sd_game WHERE status = 4 AND deleted = 0");
        }
        
        return playerGameRelatedMapper.selectList(wrapper);
    }
} 