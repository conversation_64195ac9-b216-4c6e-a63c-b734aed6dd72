package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * 球员生涯统计计算器
 * 
 * 负责从比赛统计数据聚合计算生涯数据
 * 使用通用计算组件避免代码重复
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class PlayerCareerStatsCalculator {

    @Resource
    private PlayerStatsCalculatorCommon commonCalculator;

    /**
     * 从比赛统计数据计算生涯统计
     * 
     * @param playerId 球员ID
     * @param gameType 比赛类型
     * @param gameStats 比赛统计数据列表
     * @return 生涯统计数据
     */
    public PlayerCareerStatsDO calculateFromGameStats(Long playerId, Integer gameType, List<PlayerStatisticsDO> gameStats) {
        if (gameStats == null || gameStats.isEmpty()) {
            return createEmptyCareerStats(playerId, gameType);
        }

        // 使用通用计算器进行聚合计算
        PlayerStatsCalculatorCommon.StatsAggregationResult aggregationResult = commonCalculator.aggregateStats(gameStats);
        
        // 将聚合结果转换为生涯统计DO
        PlayerCareerStatsDO careerStats = convertToCareerStatsDO(playerId, gameType, aggregationResult);
        
        // 设置生涯特有字段
        setCareerSpecificFields(careerStats, gameStats);

        log.info("📊 生涯统计计算完成: 球员={}, 比赛类型={}, 场次={}, 场均得分={}", 
                playerId, gameType, aggregationResult.getTotalGames(), aggregationResult.getAvgPoints());

        return careerStats;
    }

    /**
     * 计算连胜数据
     * 
     * @param careerStats 生涯统计数据
     * @param gameResults 比赛结果数据
     */
    public void calculateStreakData(PlayerCareerStatsDO careerStats, List<PlayerGameRelatedDO> gameResults) {
        // 使用通用计算器计算连胜数据
        PlayerStatsCalculatorCommon.StreakDataResult streakResult = commonCalculator.calculateStreakData(gameResults);
        
        // 将连胜结果设置到生涯统计中
        careerStats.setCurrentStreak(streakResult.getCurrentStreak());
        careerStats.setMaxWinStreak(streakResult.getMaxWinStreak());
        careerStats.setMaxLoseStreak(streakResult.getMaxLoseStreak());
        careerStats.setStreakStartDate(streakResult.getStreakStartDate());
        careerStats.setTotalWins(streakResult.getTotalWins());
        careerStats.setTotalLosses(streakResult.getTotalLosses());
        careerStats.setWinRate(streakResult.getWinRate());

        log.debug("生涯连胜数据计算: 当前连胜={}, 最大连胜={}, 胜率={}%", 
                streakResult.getCurrentStreak(), streakResult.getMaxWinStreak(), streakResult.getWinRate());
    }

    /**
     * 将聚合结果转换为生涯统计DO
     */
    private PlayerCareerStatsDO convertToCareerStatsDO(Long playerId, Integer gameType, 
                                                       PlayerStatsCalculatorCommon.StatsAggregationResult result) {
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(playerId);
        careerStats.setGameType(gameType);

        // 设置总计数据
        careerStats.setGamesPlayed(result.getTotalGames());
        careerStats.setValidStatsGames(result.getValidGames());
        careerStats.setTotalPoints(result.getTotalPoints());
        careerStats.setTotalRebounds(result.getTotalRebounds());
        careerStats.setTotalOffensiveRebounds(result.getTotalOffensiveRebounds());
        careerStats.setTotalDefensiveRebounds(result.getTotalDefensiveRebounds());
        careerStats.setTotalAssists(result.getTotalAssists());
        careerStats.setTotalSteals(result.getTotalSteals());
        careerStats.setTotalBlocks(result.getTotalBlocks());
        careerStats.setTotalTurnovers(result.getTotalTurnovers());
        careerStats.setTotalFouls(result.getTotalFouls());
        careerStats.setTotalFieldGoalsMade(result.getTotalFieldGoalsMade());
        careerStats.setTotalFieldGoalsAttempted(result.getTotalFieldGoalsAttempted());
        careerStats.setTotalTwoPointsMade(result.getTotalTwoPointsMade());
        careerStats.setTotalTwoPointsAttempted(result.getTotalTwoPointsAttempted());
        careerStats.setTotalThreePointsMade(result.getTotalThreePointsMade());
        careerStats.setTotalThreePointsAttempted(result.getTotalThreePointsAttempted());
        careerStats.setTotalFreeThrowsMade(result.getTotalFreeThrowsMade());
        careerStats.setTotalFreeThrowsAttempted(result.getTotalFreeThrowsAttempted());
        // 注意：数据库字段为total_playing_time(秒)，需要转换
        if (result.getTotalMinutes() != null) {
            careerStats.setTotalMinutesPlayed((int)(result.getTotalMinutes().doubleValue() * 60)); // 分钟转秒
        }

        // 设置平均数据
        careerStats.setAvgPoints(result.getAvgPoints());
        careerStats.setAvgRebounds(result.getAvgRebounds());
        careerStats.setAvgOffensiveRebounds(result.getAvgOffensiveRebounds());
        careerStats.setAvgDefensiveRebounds(result.getAvgDefensiveRebounds());
        careerStats.setAvgAssists(result.getAvgAssists());
        careerStats.setAvgSteals(result.getAvgSteals());
        careerStats.setAvgBlocks(result.getAvgBlocks());
        careerStats.setAvgTurnovers(result.getAvgTurnovers());
        careerStats.setAvgFouls(result.getAvgFouls());
        careerStats.setAvgMinutesPlayed(result.getAvgMinutes());
        careerStats.setAvgEfficiency(result.getAvgEfficiency());

        // 设置命中率数据
        careerStats.setFieldGoalPercentage(result.getFieldGoalPercentage());
        careerStats.setThreePointPercentage(result.getThreePointPercentage());
        careerStats.setTwoPointPercentage(result.getTwoPointPercentage());
        careerStats.setFreeThrowPercentage(result.getFreeThrowPercentage());
        careerStats.setTrueShootingPercentage(result.getTrueShootingPercentage());
        careerStats.setAssistTurnoverRatio(result.getAssistTurnoverRatio());

        // 设置系统字段
        careerStats.setCreator("system");
        careerStats.setUpdater("system");

        return careerStats;
    }

    /**
     * 设置生涯特有字段
     */
    private void setCareerSpecificFields(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> gameStats) {
        // 设置总赛季数（简化为1，实际需要根据比赛数据计算）
        careerStats.setTotalSeasons(1);
        
        // 设置首场和最近比赛日期（需要从比赛数据中获取）
        // 这里暂时设置为当前日期
        careerStats.setFirstGameDate(LocalDate.now());
        careerStats.setLatestGameDate(LocalDate.now());
    }

    /**
     * 计算时间相关字段
     * 更新首场比赛日期、最近比赛日期等时间字段
     * 
     * @param careerStats 生涯统计数据
     * @param gameStats 比赛统计数据列表
     */
    public void calculateTimeRelatedFields(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> gameStats) {
        if (gameStats == null || gameStats.isEmpty()) {
            log.debug("无比赛数据，跳过时间字段计算");
            return;
        }

        try {
            // 计算首场比赛日期（最早的比赛）
            LocalDate firstGameDate = gameStats.stream()
                    .filter(stat -> stat.getCreateTime() != null)
                    .map(stat -> stat.getCreateTime().toLocalDate())
                    .min(LocalDate::compareTo)
                    .orElse(LocalDate.now());

            // 计算最近比赛日期（最晚的比赛）
            LocalDate latestGameDate = gameStats.stream()
                    .filter(stat -> stat.getCreateTime() != null)
                    .map(stat -> stat.getCreateTime().toLocalDate())
                    .max(LocalDate::compareTo)
                    .orElse(LocalDate.now());

            // 更新时间字段
            careerStats.setFirstGameDate(firstGameDate);
            careerStats.setLatestGameDate(latestGameDate);

            log.debug("时间字段更新完成: 首场比赛={}, 最近比赛={}", firstGameDate, latestGameDate);

        } catch (Exception e) {
            log.error("计算时间相关字段失败", e);
        }
    }

    /**
     * 创建空的生涯统计数据
     */
    private PlayerCareerStatsDO createEmptyCareerStats(Long playerId, Integer gameType) {
        PlayerCareerStatsDO emptyStats = new PlayerCareerStatsDO();
        emptyStats.setPlayerId(playerId);
        emptyStats.setGameType(gameType);
        emptyStats.setGamesPlayed(0);
        emptyStats.setValidStatsGames(0);
        // 其他字段使用默认值
        emptyStats.setCreator("system");
        emptyStats.setUpdater("system");
        return emptyStats;
    }
} 