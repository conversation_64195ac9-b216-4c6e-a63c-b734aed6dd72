package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 球员生涯统计计算器测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class PlayerCareerStatsCalculatorTest {

    @Mock
    private PlayerStatsCalculatorCommon commonCalculator;

    @InjectMocks
    private PlayerCareerStatsCalculator playerCareerStatsCalculator;

    private PlayerStatisticsDO mockGameStats1;
    private PlayerStatisticsDO mockGameStats2;
    private PlayerGameRelatedDO mockGameRelated1;
    private PlayerGameRelatedDO mockGameRelated2;
    private PlayerStatsCalculatorCommon.StatsAggregationResult mockAggregationResult;
    private PlayerStatsCalculatorCommon.StreakDataResult mockStreakResult;

    @BeforeEach
    void setUp() {
        // 初始化模拟数据
        mockGameStats1 = createMockGameStats(1L, 100L, 25, 8, 4);
        mockGameStats2 = createMockGameStats(2L, 101L, 18, 6, 3);
        mockGameRelated1 = createMockGameRelated(1L, 100L, true);
        mockGameRelated2 = createMockGameRelated(2L, 101L, false);
        mockAggregationResult = createMockAggregationResult();
        mockStreakResult = createMockStreakResult();
    }

    @Test
    void testCalculateFromGameStats_WithValidData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> gameStats = Arrays.asList(mockGameStats1, mockGameStats2);
        
        when(commonCalculator.aggregateStats(gameStats)).thenReturn(mockAggregationResult);

        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(playerId, gameType, gameStats);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(mockAggregationResult.getTotalGames(), result.getGamesPlayed());
        assertEquals(mockAggregationResult.getTotalPoints(), result.getTotalPoints());
        assertEquals(mockAggregationResult.getAvgPoints(), result.getAvgPoints());
        assertEquals(mockAggregationResult.getTotalRebounds(), result.getTotalRebounds());
        assertEquals(mockAggregationResult.getAvgRebounds(), result.getAvgRebounds());
        assertEquals(mockAggregationResult.getTotalAssists(), result.getTotalAssists());
        assertEquals(mockAggregationResult.getAvgAssists(), result.getAvgAssists());
        assertEquals(mockAggregationResult.getFieldGoalPercentage(), result.getFieldGoalPercentage());
        assertEquals(mockAggregationResult.getThreePointPercentage(), result.getThreePointPercentage());
        assertEquals(mockAggregationResult.getFreeThrowPercentage(), result.getFreeThrowPercentage());
        assertNotNull(result.getFirstGameDate());
        assertNotNull(result.getLatestGameDate());
        assertEquals("system", result.getCreator());
        assertEquals("system", result.getUpdater());
        
        verify(commonCalculator).aggregateStats(gameStats);
    }

    @Test
    void testCalculateFromGameStats_WithEmptyGameStats() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> emptyGameStats = Collections.emptyList();

        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(playerId, gameType, emptyGameStats);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(0, result.getGamesPlayed());
        assertEquals(0, result.getValidStatsGames());
        assertEquals("system", result.getCreator());
        assertEquals("system", result.getUpdater());
        
        verifyNoInteractions(commonCalculator);
    }

    @Test
    void testCalculateFromGameStats_WithNullGameStats() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;

        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(playerId, gameType, null);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(0, result.getGamesPlayed());
        assertEquals(0, result.getValidStatsGames());
        
        verifyNoInteractions(commonCalculator);
    }

    @Test
    void testCalculateStreakData() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated1, mockGameRelated2);
        
        when(commonCalculator.calculateStreakData(gameResults)).thenReturn(mockStreakResult);

        // When
        playerCareerStatsCalculator.calculateStreakData(careerStats, gameResults);

        // Then
        assertEquals(mockStreakResult.getCurrentStreak(), careerStats.getCurrentStreak());
        assertEquals(mockStreakResult.getMaxWinStreak(), careerStats.getMaxWinStreak());
        assertEquals(mockStreakResult.getMaxLoseStreak(), careerStats.getMaxLoseStreak());
        assertEquals(mockStreakResult.getStreakStartDate(), careerStats.getStreakStartDate());
        assertEquals(mockStreakResult.getTotalWins(), careerStats.getTotalWins());
        assertEquals(mockStreakResult.getTotalLosses(), careerStats.getTotalLosses());
        assertEquals(mockStreakResult.getWinRate(), careerStats.getWinRate());
        
        verify(commonCalculator).calculateStreakData(gameResults);
    }

    @Test
    void testCalculateTimeRelatedFields_WithValidData() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        LocalDateTime earliestTime = LocalDateTime.now().minusDays(30);
        LocalDateTime latestTime = LocalDateTime.now().minusDays(1);
        
        mockGameStats1.setCreateTime(earliestTime);
        mockGameStats2.setCreateTime(latestTime);
        
        List<PlayerStatisticsDO> gameStats = Arrays.asList(mockGameStats1, mockGameStats2);

        // When
        playerCareerStatsCalculator.calculateTimeRelatedFields(careerStats, gameStats);

        // Then
        assertEquals(earliestTime.toLocalDate(), careerStats.getFirstGameDate());
        assertEquals(latestTime.toLocalDate(), careerStats.getLatestGameDate());
    }

    @Test
    void testCalculateTimeRelatedFields_WithEmptyData() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        List<PlayerStatisticsDO> emptyGameStats = Collections.emptyList();

        // When
        playerCareerStatsCalculator.calculateTimeRelatedFields(careerStats, emptyGameStats);

        // Then
        assertNull(careerStats.getFirstGameDate());
        assertNull(careerStats.getLatestGameDate());
    }

    @Test
    void testCalculateTimeRelatedFields_WithNullData() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();

        // When
        playerCareerStatsCalculator.calculateTimeRelatedFields(careerStats, null);

        // Then
        assertNull(careerStats.getFirstGameDate());
        assertNull(careerStats.getLatestGameDate());
    }

    @Test
    void testCalculateTimeRelatedFields_WithNullCreateTime() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        PlayerStatisticsDO statsWithNullTime = createMockGameStats(1L, 100L, 20, 5, 3);
        statsWithNullTime.setCreateTime(null);
        
        List<PlayerStatisticsDO> gameStats = Arrays.asList(statsWithNullTime);

        // When
        playerCareerStatsCalculator.calculateTimeRelatedFields(careerStats, gameStats);

        // Then
        // 应该使用当前日期作为默认值
        assertEquals(LocalDate.now(), careerStats.getFirstGameDate());
        assertEquals(LocalDate.now(), careerStats.getLatestGameDate());
    }

    @Test
    void testMinutesConversion_FromMinutesToSeconds() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> gameStats = Arrays.asList(mockGameStats1);
        
        // 设置模拟结果包含分钟数据
        PlayerStatsCalculatorCommon.StatsAggregationResult resultWithMinutes = createMockAggregationResult();
        resultWithMinutes.setTotalMinutes(BigDecimal.valueOf(120.5)); // 120.5分钟
        
        when(commonCalculator.aggregateStats(gameStats)).thenReturn(resultWithMinutes);

        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(playerId, gameType, gameStats);

        // Then
        // 验证分钟被正确转换为秒 (120.5 * 60 = 7230秒)
        assertEquals(7230, result.getTotalMinutesPlayed());
    }

    @Test
    void testCareerSpecificFields_AreSetCorrectly() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> gameStats = Arrays.asList(mockGameStats1, mockGameStats2);
        
        when(commonCalculator.aggregateStats(gameStats)).thenReturn(mockAggregationResult);

        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(playerId, gameType, gameStats);

        // Then
        assertEquals(1, result.getTotalSeasons()); // 应该设置为1
        assertNotNull(result.getFirstGameDate());  // 应该设置日期
        assertNotNull(result.getLatestGameDate()); // 应该设置日期
    }

    // Helper methods to create mock data
    private PlayerStatisticsDO createMockGameStats(Long id, Long gameId, int points, int rebounds, int assists) {
        PlayerStatisticsDO stats = new PlayerStatisticsDO();
        stats.setId(id);
        stats.setPlayerId(1L);
        stats.setGameId(gameId);
        stats.setSection(0);
        stats.setPoints(points);
        stats.setRebounds(rebounds);
        stats.setOffensiveRebounds(rebounds / 2);
        stats.setDefensiveRebounds(rebounds - rebounds / 2);
        stats.setAssists(assists);
        stats.setSteals(1);
        stats.setBlocks(1);
        stats.setTurnovers(2);
        stats.setFouls(2);
        stats.setFieldGoalsMade(points / 3);
        stats.setFieldGoalsAttempted(points / 2);
        stats.setThreePointsMade(2);
        stats.setThreePointsAttempted(5);
        stats.setTwoPointsMade(points / 3 - 2);
        stats.setTwoPointsAttempted(points / 2 - 5);
        stats.setFreeThrowsMade(2);
        stats.setFreeThrowsAttempted(2);
        stats.setMinutesPlayed(BigDecimal.valueOf(30.0));
        stats.setCreateTime(LocalDateTime.now());
        return stats;
    }

    private PlayerGameRelatedDO createMockGameRelated(Long id, Long gameId, boolean isWin) {
        PlayerGameRelatedDO related = new PlayerGameRelatedDO();
        related.setId(id);
        related.setPlayerId(1L);
        related.setGameId(gameId);
        related.setIsWin(isWin);
        related.setCreateTime(LocalDateTime.now());
        return related;
    }

    private PlayerStatsCalculatorCommon.StatsAggregationResult createMockAggregationResult() {
        PlayerStatsCalculatorCommon.StatsAggregationResult result = 
            new PlayerStatsCalculatorCommon.StatsAggregationResult();
        
        // 使用反射或者假设有setter方法来设置字段
        // 这里假设StatsAggregationResult有相应的setter方法
        result.setTotalGames(10);
        result.setValidGames(10);
        result.setTotalPoints(200);
        result.setTotalRebounds(50);
        result.setTotalOffensiveRebounds(20);
        result.setTotalDefensiveRebounds(30);
        result.setTotalAssists(40);
        result.setTotalSteals(15);
        result.setTotalBlocks(8);
        result.setTotalTurnovers(25);
        result.setTotalFouls(30);
        result.setTotalFieldGoalsMade(80);
        result.setTotalFieldGoalsAttempted(160);
        result.setTotalThreePointsMade(20);
        result.setTotalThreePointsAttempted(50);
        result.setTotalTwoPointsMade(60);
        result.setTotalTwoPointsAttempted(110);
        result.setTotalFreeThrowsMade(20);
        result.setTotalFreeThrowsAttempted(25);
        result.setTotalMinutes(BigDecimal.valueOf(300.0));
        result.setTotalEfficiency(BigDecimal.valueOf(150.0));
        
        result.setAvgPoints(BigDecimal.valueOf(20.0));
        result.setAvgRebounds(BigDecimal.valueOf(5.0));
        result.setAvgOffensiveRebounds(BigDecimal.valueOf(2.0));
        result.setAvgDefensiveRebounds(BigDecimal.valueOf(3.0));
        result.setAvgAssists(BigDecimal.valueOf(4.0));
        result.setAvgSteals(BigDecimal.valueOf(1.5));
        result.setAvgBlocks(BigDecimal.valueOf(0.8));
        result.setAvgTurnovers(BigDecimal.valueOf(2.5));
        result.setAvgFouls(BigDecimal.valueOf(3.0));
        result.setAvgMinutes(BigDecimal.valueOf(30.0));
        result.setAvgEfficiency(BigDecimal.valueOf(15.0));
        
        result.setFieldGoalPercentage(BigDecimal.valueOf(50.0));
        result.setThreePointPercentage(BigDecimal.valueOf(40.0));
        result.setTwoPointPercentage(BigDecimal.valueOf(54.5));
        result.setFreeThrowPercentage(BigDecimal.valueOf(80.0));
        result.setTrueShootingPercentage(BigDecimal.valueOf(55.0));
        result.setAssistTurnoverRatio(BigDecimal.valueOf(1.6));
        
        return result;
    }

    private PlayerStatsCalculatorCommon.StreakDataResult createMockStreakResult() {
        PlayerStatsCalculatorCommon.StreakDataResult result = 
            new PlayerStatsCalculatorCommon.StreakDataResult();
        
        result.setCurrentStreak(3);
        result.setMaxWinStreak(5);
        result.setMaxLoseStreak(2);
        result.setStreakStartDate(LocalDate.now().minusDays(10));
        result.setTotalWins(7);
        result.setTotalLosses(3);
        result.setWinRate(BigDecimal.valueOf(70.0));
        
        return result;
    }
}